# nqhy-inter

## 本地开发

1. 启动 SSH 隧道（如需远程 MySQL、Redis 和 Kafka）：

   **Windows用户（推荐）：**

   ```powershell
   # PowerShell脚本（自动设置UTF-8编码）
   .\start-ssh-tunnel.ps1

   # 或使用批处理文件
   start-ssh-tunnel.bat
   ```

   **其他系统：**

   ```bash
   node src/ssh-tunnel.js
   ```

   这将同时建立以下服务的SSH隧道：
   - MySQL (3306)
   - Redis (6379)
   - Kafka (9092)

   或配置好本地直连。

2. 启动服务：

   **Windows用户（推荐）：**

   ```powershell
   # PowerShell脚本（自动设置UTF-8编码）
   .\start-dev.ps1

   # 或使用批处理文件
   start-dev.bat
   ```

   **其他系统：**

   ```bash
   npm run dev
   # 或 npx tsx src/index.ts
   ```

   **如果遇到中文日志乱码，请先设置编码：**

   ```bash
   chcp 65001
   npm run dev
   ```

   默认监听 3000 端口，可通过 `PORT` 环境变量自定义。

3. 访问接口：
   <http://localhost:3000>

## 环境变量

### 基础配置

- `NODE_ENV`：运行环境（development/production，默认 development）
- `ENVIRONMENT`：自定义环境标识，用于环境隔离（优先级高于NODE_ENV）
- `PORT`：服务监听端口（可选，默认 3000）

### 日志配置

- `LOG_LEVEL`：日志级别（trace/debug/info/warn/error/fatal，默认：开发环境 debug，生产环境 info）
- `LOG_FORMAT`：日志格式（pretty/json，默认：开发环境 pretty，生产环境 json）

### 数据库配置

- `DATABASE_URL`：MySQL 连接串，如：`mysql://user:pass@127.0.0.1:3306/dbname`
- `REDIS_PASSWORD`：Redis 密码

### SSH隧道配置（可选）

- `SSH_HOST`、`SSH_PORT`、`SSH_USER`、`SSH_PASS`：如需 SSH 隧道，配置远程主机信息

### Kafka配置

- `KAFKA_ENABLED`：是否启用Kafka（true/false，默认 true）
- `KAFKA_BOOTSTRAP_SERVERS`：Kafka服务器地址（默认 127.0.0.1:9092，通过SSH隧道访问）
- `KAFKA_USERNAME`：Kafka用户名（默认 admin）
- `KAFKA_PASSWORD`：Kafka密码（默认 12345678）
- `KAFKA_GROUP_ID`：消费者组ID（默认 nqhy-inter-group）

### 定时任务配置

- `SCHEDULER_ENABLED`：是否启用定时任务调度器（true/false，默认 true）
- `SCHEDULER_MAIN_INSTANCE`：是否为主实例，只有主实例执行定时任务（true/false，默认 true）
- `SCHEDULER_MODE`：调度器模式（kafka/direct/disabled，默认 kafka）
- `INSTANCE_ID`：实例标识符，用于分布式锁（默认 instance-1）

## 日志系统

项目集成了强大的日志系统，基于 Pino 构建，支持结构化日志和多种输出格式。

### 日志级别

- `trace`：最详细的调试信息
- `debug`：调试信息
- `info`：一般信息（默认）
- `warn`：警告信息
- `error`：错误信息
- `fatal`：致命错误

### 使用方式

```typescript
import { apiLogger, logger, redisLogger } from '@/lib/logger'

// 基础日志
logger.info('应用启动', { port: 3000 })
logger.error('发生错误', { message: error.message })

// 组件专用日志
apiLogger.info('API请求', { method: 'GET', url: '/api/test' })
redisLogger.warn('Redis连接警告', { host: '127.0.0.1' })
```

### 可用的专用日志器

- `apiLogger`：API相关日志
- `redisLogger`：Redis相关日志
- `dbLogger`：数据库相关日志
- `sshLogger`：SSH隧道相关日志
- `schedulerLogger`：定时任务相关日志
- `douyinLogger`：抖音服务相关日志

## Kafka集成

项目集成了Kafka消息队列，支持消息的生产和消费。

### 支持的主题

- `douyin-message`：抖音消息相关
- `douyin-token`：抖音Token相关
- `system-log`：系统日志
- `user-action`：用户行为

### API端点

#### 发送单条消息

```bash
POST /kafka/send
Content-Type: application/json

{
  "topic": "douyin-message",
  "message": {
    "type": "subscribe",
    "appId": "tt5e4928f4c142201d02",
    "data": {...}
  },
  "key": "optional-message-key",
  "partition": 0
}
```

#### 批量发送消息

```bash
POST /kafka/send-batch
Content-Type: application/json

{
  "topic": "douyin-message",
  "messages": [
    {
      "key": "msg1",
      "value": {"type": "subscribe", "data": {...}},
      "partition": 0
    },
    {
      "key": "msg2",
      "value": {"type": "token_refresh", "data": {...}}
    }
  ]
}
```

#### 获取可用主题

```bash
GET /kafka/topics
```

### 使用示例

```typescript
import { sendBatchMessages, sendMessage, topics } from '@/services/kafka'

// 发送单条消息
await sendMessage(topics.DOUYIN_MESSAGE, {
  type: 'subscribe',
  appId: 'tt5e4928f4c142201d02',
  userId: '12345'
}, 'message-key')

// 批量发送消息
await sendBatchMessages(topics.DOUYIN_TOKEN, [
  { key: 'token1', value: { appId: 'app1', token: 'xxx' } },
  { key: 'token2', value: { appId: 'app2', token: 'yyy' } }
])
```

## 定时任务管理

项目集成了智能的定时任务调度系统，支持两种模式：**传统直接执行**和**Kafka解耦模式**。

### 🚀 调度器模式对比

#### 传统模式 (SCHEDULER_MODE=direct)

```text
调度器 → 直接执行任务 → 结果
```

- ✅ 简单直接，延迟低
- ❌ 调度和执行耦合
- ❌ 扩展性有限

#### Kafka模式 (SCHEDULER_MODE=kafka) 🌟 推荐

```text
调度器 → 发送消息到Kafka → 任务消费者 → 执行任务 → 结果
```

- ✅ 调度和执行完全解耦
- ✅ 消息持久化，不会丢失
- ✅ 可以水平扩展消费者
- ✅ 支持任务重试和错误处理
- ✅ 更好的监控和调试能力

#### 禁用模式 (SCHEDULER_MODE=disabled)

```text
不启动任何调度器
```

- ✅ 完全禁用定时任务
- ✅ 适用于只处理API请求的实例
- ✅ 节省资源

### 防重复执行机制

1. **环境隔离**：不同环境（开发/生产）的定时任务完全隔离，互不影响
2. **分布式锁**：使用Redis实现分布式锁，确保同一环境内同一时间只有一个实例执行任务
3. **实例标识**：通过环境变量控制哪些实例可以执行定时任务
4. **任务隔离**：每个任务使用独立的锁，互不影响

### 部署配置

#### 开发环境（调试服务器）

```bash
# 开发环境配置 - 使用Kafka模式
NODE_ENV=development
ENVIRONMENT=development
SCHEDULER_ENABLED=true
SCHEDULER_MAIN_INSTANCE=true
SCHEDULER_MODE=kafka
INSTANCE_ID=dev-server-1

# 分布式锁键前缀：development_scheduler_lock:
```

#### 生产环境 - 主实例（部署服务器）

```bash
# 生产环境主实例配置 - 使用Kafka模式
NODE_ENV=production
ENVIRONMENT=production
SCHEDULER_ENABLED=true
SCHEDULER_MAIN_INSTANCE=true
SCHEDULER_MODE=kafka
INSTANCE_ID=prod-main-1

# 分布式锁键前缀：production_scheduler_lock:
```

#### 生产环境 - 从实例（负载均衡）

```bash
# 生产环境从实例配置
NODE_ENV=production
ENVIRONMENT=production
SCHEDULER_ENABLED=false
SCHEDULER_MAIN_INSTANCE=false
INSTANCE_ID=prod-worker-1

# 不执行定时任务，只处理API请求
```

#### 测试环境（可选）

```bash
# 测试环境配置
NODE_ENV=test
ENVIRONMENT=test
SCHEDULER_ENABLED=true
SCHEDULER_MAIN_INSTANCE=true
INSTANCE_ID=test-server-1

# 分布式锁键前缀：test_scheduler_lock:
```

### 部署场景说明

#### 场景1：开发和生产在不同服务器

```text
开发服务器 (192.168.1.100)     生产服务器 (192.168.1.200)
├── 应用实例 (dev)              ├── 应用实例 (prod-main)
├── ENVIRONMENT=development     ├── ENVIRONMENT=production
├── 执行开发环境定时任务         ├── 执行生产环境定时任务
└── 锁前缀: dev_scheduler_lock  └── 锁前缀: prod_scheduler_lock

共享Redis服务器 (通过SSH隧道)
├── development_scheduler_lock:task_xxx  ← 开发环境锁
└── production_scheduler_lock:task_xxx   ← 生产环境锁
```

**结果**：两个环境的定时任务完全隔离，互不干扰

#### 场景2：生产环境多实例负载均衡

```text
生产主实例 (prod-main-1)       生产从实例 (prod-worker-1)
├── SCHEDULER_ENABLED=true      ├── SCHEDULER_ENABLED=false
├── 执行定时任务                ├── 只处理API请求
└── 获取分布式锁                └── 不参与定时任务

生产从实例 (prod-worker-2)
├── SCHEDULER_ENABLED=false
├── 只处理API请求
└── 不参与定时任务
```

**结果**：只有主实例执行定时任务，从实例专注处理API请求

### API接口

#### 手动触发任务

```bash
POST /scheduler/trigger
Content-Type: application/json

{
  "taskType": "douyin_message",
  "parameters": {
    "tplId": "custom_template_id",
    "messageData": [
      {"keyword1": "测试消息", "keyword2": "手动触发"}
    ]
  }
}
```

#### 获取调度器配置

```bash
GET /scheduler/config
```

#### 获取Kafka主题列表

```bash
GET /kafka/topics
```

### 任务监控

定时任务的执行情况会记录在日志中，包括：

- 环境标识和实例信息
- 任务开始和完成时间
- 分布式锁获取状态
- 执行结果统计
- 错误信息

## Docker 部署

1. 构建镜像：

   ```bash
   docker build -t nqhy-inter .
   ```

2. 运行容器（建议用 .env 文件管理环境变量）：

   ```bash
   docker run --env-file .env -p 3000:3000 nqhy-inter
   ```

   镜像会自动启动 SSH 隧道和服务监听。

---
如需自定义 schema、接口或分表逻辑，请编辑 `src/schema.ts` 和 `src/db-utils.ts`。
