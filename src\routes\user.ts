import { Hono } from 'hono'
import { resolver, validator as zValidator } from 'hono-openapi/zod'
import { generateJWT, jwtAuth } from '@/lib/jwt'
import logger from '@/lib/logger'
import { userService } from '@/services/user/user-service'
import { globalResponse } from '@/types'
import { createUserRequestSchema, userProfileSchema } from '@/types/user'

const router = new Hono()
const tags = ['用户']
// 创建或获取用户
router.post('/profile/get', globalResponse({
  description: '创建或获取用户',
  tags,
}), zValidator('json', createUserRequestSchema), async (c) => {
  try {
    const body = await c.req.json()
    const { uid, userName, token, isGuest, isNewUser, isPreReg, openType, avatar, nickname } = body

    if (!uid) {
      return c.json({
        success: false,
        message: '缺少用户UID',
      }, 400)
    }

    const createData = uid
      ? {
          uid,
          userName,
          token,
          isGuest: isGuest ?? false,
          isNewUser: isNewUser ?? true,
          isPreReg: isPreReg ?? false,
          openType,
          avatar,
          nickname,
        }
      : undefined

    const user = await userService.getUserProfile(uid, createData)

    const jwtToken = generateJWT({
      id: user.id,
      uid: user.uid,
      userName: user.userName,
      isGuest: user.isGuest,
      isNewUser: user.isNewUser,
      isPreReg: user.isPreReg,
      openType: user.openType,
    })

    return c.json({
      success: true,
      data: {
        ...user,
        lastLoginAt: user.lastLoginAt?.toISOString(),
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
        accessToken: jwtToken,
        isNewlyCreated: user.loginCount === 1, // 是否为新创建的用户
      },
    })
  }
  catch (error) {
    logger.error('获取/创建用户信息失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
    }, 500)
  }
})

// 更新用户信息
router.post('/profile/update', jwtAuth(), globalResponse({
  description: '更新用户信息',
  tags,
}), zValidator('json', userProfileSchema), async (c) => {
  try {
    const body = await c.req.json()
    const id = body.id

    if (!id) {
      return c.json({
        success: false,
        message: '缺少用户ID',
      }, 400)
    }

    const user = await userService.updateUser(id, body)

    if (!user) {
      return c.json({
        success: false,
        message: '用户不存在',
      }, 404)
    }

    return c.json({
      success: true,
      data: {
        ...user,
        lastLoginAt: user.lastLoginAt?.toISOString(),
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      },
    })
  }
  catch (error) {
    logger.error('更新用户信息失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
    }, 500)
  }
})

// 删除用户缓存
// router.post('/cache/delete', jwtAuth(), globalResponse({
//   description: '删除用户缓存',
//   tags,
// }), async (c) => {
//   try {
//     const { uid } = await c.req.json()

//     if (!uid) {
//       return c.json({
//         success: false,
//         message: '缺少用户ID',
//       }, 400)
//     }

//     await userService.clearUserCache(uid)

//     return c.json({
//       success: true,
//       message: '用户缓存已清除',
//     })
//   }
//   catch (error) {
//     logger.error('清除用户缓存失败', { message: error.message })
//     return c.json({
//       success: false,
//       message: error.message,
//     }, 500)
//   }
// })

export default router
