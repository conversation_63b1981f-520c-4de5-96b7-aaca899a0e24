import { createNodeWebSocket } from '@hono/node-ws'
import { Hono } from 'hono'
import { formatDate, getSignature } from '@/common'
import logger from '@/lib/logger'
import { gameArchiveService } from '@/services/archive/game-archive-service'
import { globalResponse } from '@/types'
import { getTime } from '@/utils/time'

const router = new Hono()

// WebSocket 消息类型定义
interface WSMessage {
  id: string // 消息ID，用于响应匹配
  type: 'create' | 'get' | 'list' | 'update' | 'delete'
  ['X-Signature']: string
  data: {
    userId: string
    gameId: string
    archiveId?: string // 可选，创建时前端可传递自定义ID，不传则自动生成UUID
    // 更新模式：patch=增量更新（默认），replace=全量替换
    updateMode?: 'patch' | 'replace'

    // 存档数据：
    // - replace模式：完全替换整个存档数据
    // - patch模式：存在的字段替换，不存在的字段保持存档原来的值
    archiveData?: Record<string, any>

    lastPlayed?: string
    immediate?: boolean // true 时 operationLog必传
    priority?: 'high' | 'normal' | 'low'
    clientVersion?: string
    operationLog?: Array<{ // 操作日志，用于验证数据变化的合法性
      path?: string
      amount?: any // 变化数量
      source?: string // 来源：quest_reward, purchase, battle_win等
      timestamp: number // 操作时间戳
    }>
  }
}

interface WSResponse {
  id: string // 对应请求的消息ID
  success: boolean
  data?: any
  message?: string
}
const { injectWebSocket, upgradeWebSocket } = createNodeWebSocket({ app: router })

function validateMessage(message: any): message is WSMessage {
  return (
    typeof message === 'object'
    && typeof message.id === 'string'
    && typeof message.type === 'string'
    && ['create', 'get', 'list', 'update', 'delete', 'verify'].includes(message.type)
    && message.data !== undefined
  )
}

// 发送响应
function sendResponse(ws: any, id: string, success: boolean, data?: any, error?: string) {
  const response: WSResponse = { id, success }
  if (data !== undefined)
    response.data = data
  if (error)
    response.message = error

  try {
    ws.send(JSON.stringify(response))
  }
  catch (err) {
    logger.error('WebSocket 发送响应失败', { message: err.message, messageId: id })
  }
}

// // WebSocket 路由
router.get('/game-archives/connect', upgradeWebSocket((c) => {
  return {
    onOpen: async (_evt, ws) => {
      // if (!auth.valid) {
      //   logger.warn('WebSocket 连接认证失败', { message: auth.error })

      //   // 发送认证失败消息并关闭连接
      //   ws.send(JSON.stringify({
      //     type: 'auth_failed',
      //     message: auth.error,
      //     message: '身份认证失败，连接将被关闭',
      //   }))

      //   // 延迟关闭连接，确保消息发送完成
      //   setTimeout(() => {
      //     ws.close(1008, 'Authentication failed')
      //   }, 100)

      //   return
      // }

      // 认证成功，保存用户信息到 WebSocket 连接
      // ;(ws as any).user = auth.user

      // logger.info('游戏存档 WebSocket 连接建立', {
      //   userId: auth.user.id,
      //   username: auth.user.username,
      // })

      ws.send(JSON.stringify({
        type: 'connected',
        message: '游戏存档服务已连接',
      }))
    },

    onMessage: async (evt, ws) => {
      try {
        // 检查用户是否已认证
        // const user = (ws as any).user
        // if (!user) {
        //   ws.send(JSON.stringify({
        //     type: 'error',
        //     message: '用户未认证',
        //   }))
        //   return
        // }

        const rawMessage = evt.data.toString()
        const message = JSON.parse(rawMessage)

        if (!validateMessage(message)) {
          sendResponse(ws, message.id || 'unknown', false, null, '无效的消息格式')
          return
        }

        logger.debug('收到 WebSocket 消息', {
          type: message.type,
          id: message.id,
          // userId: user.id,
        })

        // if (process.env.NODE_ENV === 'production' && message['X-Signature'] !== getSignature(message.data, message.data.gameId, message.data.userId)) {
        //   sendResponse(ws, message.id, false, null, '签名验证失败')
        //   return
        // }

        switch (message.type) {
          case 'create':
            await handleCreateArchive(ws, message)
            break
          case 'get':
            await handleGetArchive(ws, message)
            break
          case 'update':
            await handleUpdateArchive(ws, message)
            break
          case 'delete':
            await handleDeleteArchive(ws, message)
            break
          default:
            sendResponse(ws, message.id, false, null, '不支持的操作类型')
        }
      }
      catch (error) {
        logger.error(`WebSocket 消息处理失败${error.message}`, { message: error.message })
        try {
          const message = JSON.parse(evt.data.toString())
          sendResponse(ws, message.id || 'unknown', false, null, '消息处理失败')
        }
        catch {
          // 无法解析消息ID，发送通用错误
          ws.send(JSON.stringify({
            success: false,
            message: '消息处理失败',
          }))
        }
      }
    },

    onClose: (_evt, ws) => {
      logger.info('游戏存档 WebSocket 连接关闭')
    },

    onError: (evt, _ws) => {
      logger.error('游戏存档 WebSocket 错误', { message: evt })
    },
  }
}))

// 处理创建存档
async function handleCreateArchive(ws: any, message: WSMessage) {
  try {
    const { userId, gameId, archiveId, archiveData, lastPlayed } = message.data

    if (!userId || !gameId || !archiveData) {
      sendResponse(ws, message.id, false, null, '缺少必要参数')
      return
    }

    const archive = await gameArchiveService.createArchive({
      userId,
      gameId,
      archiveId, // 可选，前端可传递自定义ID，不传则自动生成UUID
      archiveData,
      lastPlayed: lastPlayed ? getTime(lastPlayed) : undefined,
    })

    if (!archive) {
      sendResponse(ws, message.id, false, null, '该用户的游戏存档已存在')
      return
    }

    sendResponse(ws, message.id, true, {
      ...archive,
      lastPlayed: formatDate(archive.lastPlayed),
      createdAt: formatDate(archive.createdAt),
      updatedAt: formatDate(archive.updatedAt),
    })
  }
  catch (error) {
    logger.error('创建存档失败', { message: error.message, messageId: message.id, userId: message.data.userId })

    // 检查是否是唯一约束违反错误
    if (error.message && error.message.includes('unique_user_game')) {
      sendResponse(ws, message.id, false, null, `该用户的游戏存档已存在，请先删除现有存档`)
    }
    else {
      sendResponse(ws, message.id, false, null, error.message)
    }
  }
}

// 处理获取存档详情
async function handleGetArchive(ws: any, message: WSMessage) {
  try {
    const { userId, gameId } = message.data

    if (!userId || !gameId) {
      sendResponse(ws, message.id, false, null, '缺少必要参数')
      return
    }

    const archive = await gameArchiveService.getArchiveByUserGame(userId, gameId)

    if (!archive) {
      sendResponse(ws, message.id, false, null, `存档不存在`)
      return
    }

    sendResponse(ws, message.id, true, {
      ...archive,
      lastPlayed: formatDate(archive.lastPlayed),
      createdAt: formatDate(archive.createdAt),
      updatedAt: formatDate(archive.updatedAt),
    })
  }
  catch (error) {
    logger.error('获取存档失败', { message: error.message, messageId: message.id, userId: message.data.userId })
    sendResponse(ws, message.id, false, null, error.message)
  }
}

// 处理更新存档
async function handleUpdateArchive(ws: any, message: WSMessage) {
  try {
    const {
      userId,
      gameId,
      updateMode = 'patch', // 默认为增量更新
      archiveData,
      lastPlayed,
      immediate = false,
      priority = 'normal',
      clientVersion, // 客户端版本号
      operationLog, // 操作日志，用于防内存修改验证
    } = message.data

    if (!userId || !gameId) {
      sendResponse(ws, message.id, false, null, '缺少必要参数：userId 和 gameId')
      return
    }

    // 验证更新数据
    if (!archiveData) {
      sendResponse(ws, message.id, false, null, '需要提供 archiveData')
      return
    }

    const archive = await gameArchiveService.updateArchive(userId, gameId, {
      updateMode,
      archiveData,
      lastPlayed: lastPlayed ? getTime(lastPlayed) : undefined,
      immediate, // 是否立即写入数据库（付费相关等重要操作）
      priority, // 队列优先级
      clientVersion, // 客户端版本号
      operationLog, // 操作日志，用于防内存修改验证
    })

    if (!archive) {
      sendResponse(ws, message.id, false, null, '存档更新失败')
      return
    }

    sendResponse(ws, message.id, true, {
      ...archive,
      lastPlayed: formatDate(archive.lastPlayed),
      createdAt: formatDate(archive.createdAt),
      updatedAt: formatDate(archive.updatedAt),
      updateMode, // 返回更新模式
      immediate, // 返回是否为立即更新
      validated: immediate, // 是否经过了数据校验
    })
  }
  catch (error) {
    logger.error('更新存档失败', { message: error.message, messageId: message.id, userId: message.data.userId })

    // 特殊处理校验失败的情况
    if ((error as any).code === 'VALIDATION_FAILED') {
      sendResponse(ws, message.id, false, {
        serverData: (error as any).serverData, // 返回服务器端的正确数据
        validationFailed: true,
      }, error.message)
    }
    else {
      sendResponse(ws, message.id, false, null, error.message)
    }
  }
}

// 处理删除存档
async function handleDeleteArchive(ws: any, message: WSMessage) {
  try {
    const { userId, gameId } = message.data

    if (!userId || !gameId) {
      sendResponse(ws, message.id, false, null, '缺少必要参数')
      return
    }

    const deleted = await gameArchiveService.deleteArchive(userId, gameId)

    if (!deleted) {
      sendResponse(ws, message.id, false, null, '存档删除失败')
      return
    }

    sendResponse(ws, message.id, true, { message: `存档删除成功` })
  }
  catch (error) {
    logger.error('删除存档失败', { message: error.message, messageId: message.id, userId: message.data.userId })
    sendResponse(ws, message.id, false, null, error.message)
  }
}

export default router
export { injectWebSocket }
