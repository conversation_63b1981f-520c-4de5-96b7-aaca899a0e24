import { Hono } from 'hono'
import logger from '@/lib/logger'
import { archiveQueue } from '@/services/queue/archive-queue'
import { queueManager } from '@/services/queue/queue-manager'
import { getTime } from '@/utils/time'

const router = new Hono()

// 获取队列状态
router.get('/queue/status', async (c) => {
  try {
    const status = await queueManager.getStatus()

    return c.json({
      success: true,
      data: {
        ...status,
        managerRunning: queueManager.isRunning(),
        timestamp: getTime().toISOString(),
      },
    })
  }
  catch (error) {
    logger.error('获取队列状态失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
    }, 500)
  }
})

// 获取队列统计
router.get('/queue/stats', async (c) => {
  try {
    const stats = await archiveQueue.getQueueStats()

    return c.json({
      success: true,
      data: stats,
    })
  }
  catch (error) {
    logger.error('获取队列统计失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
    }, 500)
  }
})

// 手动处理队列
router.post('/queue/process', async (c) => {
  try {
    await queueManager.processQueues()

    return c.json({
      success: true,
      message: '队列处理完成',
    })
  }
  catch (error) {
    logger.error('手动处理队列失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
    }, 500)
  }
})

// 清空队列
router.delete('/queue/clear', async (c) => {
  try {
    const priority = c.req.query('priority') as 'high' | 'normal' | 'low' | undefined

    await archiveQueue.clearQueue(priority)

    return c.json({
      success: true,
      message: priority ? `已清空 ${priority} 优先级队列` : '已清空所有队列',
    })
  }
  catch (error) {
    logger.error('清空队列失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
    }, 500)
  }
})

// 重启队列管理器
router.post('/queue/restart', async (c) => {
  try {
    await queueManager.restart()

    return c.json({
      success: true,
      message: '队列管理器重启完成',
    })
  }
  catch (error) {
    logger.error('重启队列管理器失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
    }, 500)
  }
})

// 队列健康检查
router.get('/queue/health', async (c) => {
  try {
    const health = await archiveQueue.healthCheck()

    return c.json({
      success: true,
      data: health,
    }, health.healthy ? 200 : 503)
  }
  catch (error) {
    logger.error('队列健康检查失败', { message: error.message })
    return c.json({
      success: false,
      message: error.message,
      healthy: false,
    }, 503)
  }
})

// router.get('/queue/dashboard', async (c) => {
//   try {
//     const status = await queueManager.getStatus()

//     const html = `
// <!DOCTYPE html>
// <html>
// <head>
//     <title>队列监控面板</title>
//     <meta charset="utf-8">
//     <meta name="viewport" content="width=device-width, initial-scale=1">
//     <style>
//         body { font-family: Arial, sans-serif; margin: 20px; }
//         .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
//         .healthy { background-color: #d4edda; color: #155724; }
//         .unhealthy { background-color: #f8d7da; color: #721c24; }
//         .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
//         .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
//         .stat-value { font-size: 24px; font-weight: bold; color: #007bff; }
//         .stat-label { color: #6c757d; margin-top: 5px; }
//         .actions { margin: 20px 0; }
//         .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
//         .btn-primary { background: #007bff; color: white; }
//         .btn-warning { background: #ffc107; color: black; }
//         .btn-danger { background: #dc3545; color: white; }
//         .issues { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0; }
//     </style>
// </head>
// <body>
//     <h1>队列监控面板</h1>

//     <div class="status ${status.healthy ? 'healthy' : 'unhealthy'}">
//         <strong>状态:</strong> ${status.healthy ? '健康' : '异常'}
//         ${status.issues.length > 0 ? `<br><strong>问题:</strong> ${status.issues.join(', ')}` : ''}
//     </div>

//     <div class="stats">
//         <div class="stat-card">
//             <div class="stat-value">${status.archive.stats?.high || 0}</div>
//             <div class="stat-label">高优先级队列</div>
//         </div>
//         <div class="stat-card">
//             <div class="stat-value">${status.archive.stats?.normal || 0}</div>
//             <div class="stat-label">普通优先级队列</div>
//         </div>
//         <div class="stat-card">
//             <div class="stat-value">${status.archive.stats?.low || 0}</div>
//             <div class="stat-label">低优先级队列</div>
//         </div>
//         <div class="stat-card">
//             <div class="stat-value">${status.archive.stats?.total || 0}</div>
//             <div class="stat-label">总队列长度</div>
//         </div>
//     </div>

//     <div class="actions">
//         <button class="btn btn-primary" onclick="processQueue()">手动处理队列</button>
//         <button class="btn btn-warning" onclick="restartManager()">重启管理器</button>
//         <button class="btn btn-danger" onclick="clearQueue()">清空队列</button>
//         <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
//     </div>

//     <script>
//         async function processQueue() {
//             try {
//                 const response = await fetch('/api/queue/process', { method: 'POST' });
//                 const result = await response.json();
//                 alert(result.success ? '处理完成' : '处理失败: ' + result.error);
//                 location.reload();
//             } catch (error) {
//                 alert('请求失败: ' + error.message);
//             }
//         }

//         async function restartManager() {
//             if (!confirm('确定要重启队列管理器吗？')) return;
//             try {
//                 const response = await fetch('/api/queue/restart', { method: 'POST' });
//                 const result = await response.json();
//                 alert(result.success ? '重启完成' : '重启失败: ' + result.error);
//                 location.reload();
//             } catch (error) {
//                 alert('请求失败: ' + error.message);
//             }
//         }

//         async function clearQueue() {
//             if (!confirm('确定要清空所有队列吗？这将丢失未处理的任务！')) return;
//             try {
//                 const response = await fetch('/api/queue/clear', { method: 'DELETE' });
//                 const result = await response.json();
//                 alert(result.success ? '清空完成' : '清空失败: ' + result.error);
//                 location.reload();
//             } catch (error) {
//                 alert('请求失败: ' + error.message);
//             }
//         }

//         // 自动刷新
//         setInterval(() => {
//             location.reload();
//         }, 30000); // 30秒刷新一次
//     </script>
// </body>
// </html>
//     `

//     return c.html(html)
//   }
//   catch (error) {
//     logger.error('获取队列面板失败', { message: error.message })
//     return c.text(`队列面板加载失败: ${error.message}`, 500)
//   }
// })

export default router
