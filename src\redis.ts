import { createClient } from 'redis'
import { redisLogger } from '@/lib/logger'

let redisClient: ReturnType<typeof createClient> | null = null
let redisReadClient: ReturnType<typeof createClient> | null = null

export async function getRedis() {
  if (!redisClient) {
    const redisHost = process.env.REDIS_HOST || 'redis-internal'
    const redisPort = Number.parseInt(process.env.REDIS_PORT || '6379')

    redisClient = createClient({
      socket: {
        host: redisHost,
        port: redisPort,
      },
      password: process.env.REDIS_PASSWORD,
    })

    redisClient.on('error', (err) => {
      console.error('Redis连接错误:', err)
      redisLogger.error('Redis连接错误', { message: err.message, code: err.code })
    })

    redisClient.on('connect', () => {
      redisLogger.info('Redis连接成功')
    })

    redisClient.on('ready', () => {
      redisLogger.info('Redis客户端就绪')
    })

    redisClient.on('reconnecting', () => {
      redisLogger.warn('Redis正在重连')
    })

    redisClient.on('end', () => {
      redisLogger.info('Redis连接已关闭')
    })

    // 添加连接超时
    const connectPromise = redisClient.connect()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Redis 连接超时')), 5000)
    })

    await Promise.race([connectPromise, timeoutPromise])
  }
  return redisClient
}

// 只读 Redis 连接（连接到副本）
export async function getReadRedis() {
  if (!redisReadClient) {
    // 如果有副本连接配置，使用副本；否则使用主库
    const redisHost = process.env.REDIS_REPLICA_HOST || process.env.REDIS_HOST || 'redis-replica'
    const redisPort = Number.parseInt(process.env.REDIS_REPLICA_PORT || process.env.REDIS_PORT || '6379')

    redisReadClient = createClient({
      socket: {
        host: redisHost,
        port: redisPort,
      },
      password: process.env.REDIS_PASSWORD,
    })

    redisReadClient.on('error', (err) => {
      console.error('Redis 副本连接错误:', err)
      redisLogger.error('Redis 副本连接错误', { message: err.message, code: err.code })
    })

    redisReadClient.on('connect', () => {
      redisLogger.info('Redis 副本连接成功')
    })

    redisReadClient.on('ready', () => {
      redisLogger.info('Redis 副本客户端就绪')
    })

    await redisReadClient.connect()
  }
  return redisReadClient
}

export async function closeRedis() {
  if (redisClient) {
    await redisClient.quit()
    redisClient = null
  }
  if (redisReadClient) {
    await redisReadClient.quit()
    redisReadClient = null
  }
}

/**
 * 获取抖音access token
 * @param appId 应用ID，如 'tt5e4928f4c142201d02'
 * @returns access token字符串，如果不存在则返回null
 */
export async function getAccessToken(appId: string): Promise<string | null> {
  try {
    const redis = await getRedis()
    const key = `DY_TOKEN_PREFIX:${appId}`
    const value = await redis.get(key)

    if (!value) {
      redisLogger.warn('未找到access token', { appId, key })
      return null
    }

    // 解析JSON字符串
    const tokenData = JSON.parse(value.toString())

    // 提取access_token字段
    if (tokenData && tokenData.access_token) {
      redisLogger.debug('成功获取access token', { appId, tokenLength: tokenData.access_token.length })
      return tokenData.access_token
    }
    else {
      redisLogger.warn('token数据格式不正确', { appId, tokenData })
      return null
    }
  }
  catch (error) {
    redisLogger.error('获取access token失败', { appId, message: error.message })
    return null
  }
}
