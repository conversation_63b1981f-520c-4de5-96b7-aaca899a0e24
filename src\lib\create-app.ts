import type { Schema } from 'hono'

import type { AppBind<PERSON>, AppOpenAPI } from './types'
import { Hono } from 'hono'
import { requestId } from 'hono/request-id'
import { notFound, onError, serveEmojiFavicon } from 'stoker/middlewares'

import { pinoLogger } from '@/middlewares/pino-logger'
import { errorLogger, requestLogger } from '@/middlewares/request-logger'

export function createRouter() {
  return new Hono<AppBindings>()
}

export default function createApp() {
  const app = createRouter()
  app.use(requestId())
    .use(serveEmojiFavicon('📝'))
    // .use(pinoLogger())
    .use(errorLogger())
    .use(requestLogger())

  app.notFound(notFound)
  app.onError(onError)
  return app
}
