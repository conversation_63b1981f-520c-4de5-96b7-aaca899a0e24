import { boolean, decimal, index, integer, json, jsonb, pgTable, serial, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core'

// 事件数据模型 - 大表优化 (每月600万数据)
export const eventData = pgTable(
  'event_data',
  {
    userId: varchar('user_id', { length: 255 }).notNull(),
    gameId: varchar('game_id', { length: 100 }).notNull(),
    eventCode: varchar('event_code', { length: 100 }).notNull(),
    uid: varchar('uid', { length: 255 }).notNull(),
    eventTime: timestamp('event_time', { withTimezone: true }).notNull().defaultNow(),
    data: json('data').notNull(),
  },
  table => [
    // 复合索引优化查询性能
    index('idx_event_data_event_time').on(table.eventTime),
    index('idx_event_data_open_id_event_code').on(table.uid, table.eventCode),
    index('idx_event_data_app_id_event_time').on(table.gameId, table.eventTime),
    index('idx_event_data_event_code_event_time').on(table.eventCode, table.eventTime),
    // 分区键索引 - 按月分区
    index('idx_event_data_event_time_month').on(table.eventTime),
  ],
)

// 定时任务配置表 - 合并设计，消息数据存储为JSON数组
export const scheduledTasks = pgTable(
  'nqhy_message_tasks',
  {
    id: serial('id').primaryKey(),
    tplId: varchar('tpl_id', { length: 255 }).notNull(),
    platform: varchar('platform', { length: 10 }).notNull(),
    appId: varchar('app_id', { length: 255 }).notNull(),
    max: integer('max').notNull().default(1),
    cronExpression: varchar('cron_expression', { length: 100 }).notNull(),
    messageData: jsonb('message_data').notNull(), // 存储消息数组，如 [{"功能名称": "七日签到", "签到奖励": "盲盒x10"}, {...}]
    intervalHours: integer('interval_hours').notNull().default(24), // 执行间隔时间（小时），默认24小时（每日一次）
    enabled: boolean('enabled').notNull().default(true),
    type: varchar('type', { length: 20 }).notNull().default('recurring'), // 任务类型：recurring(循环) 或 single(单次)
    taskType: varchar('task_type', { length: 20 }).notNull().default('message'), // 任务执行类型：message(消息) 或 card(卡片)
    meta: jsonb('meta'), // 元数据，JSON格式，包含任务的额外配置信息
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
    lastExecutedAt: timestamp('last_executed_at', { withTimezone: true }),
  },
  table => [
    index('idx_tpl_id').on(table.tplId),
    index('idx_enabled').on(table.enabled),
    index('idx_app_id').on(table.appId),
    index('idx_created_at').on(table.createdAt),
  ],
)

// 用户信息表
export const userProfiles = pgTable(
  'user_profiles',
  {
    id: uuid('id').primaryKey().defaultRandom(), // 用户ID，使用UUID格式，提高性能和可读性
    uid: varchar('uid', { length: 50 }).notNull().unique(), // 用户唯一ID
    userName: varchar('user_name', { length: 255 }).notNull(), // 用户名/邮箱
    token: varchar('token', { length: 500 }), // 用户token
    isGuest: boolean('is_guest').notNull().default(false), // 是否游客
    isNewUser: boolean('is_new_user').notNull().default(true), // 是否新用户
    isPreReg: boolean('is_pre_reg').notNull().default(false), // 是否预注册
    openType: varchar('open_type', { length: 10 }), // 开放类型
    gameId: varchar('game_id', { length: 100 }).default(''), // 游戏ID
    ip: varchar('ip', { length: 100 }), // 注册IP
    country: varchar('country', { length: 100 }), // 国家
    avatar: varchar('avatar', { length: 500 }), // 头像URL
    nickname: varchar('nickname', { length: 100 }), // 昵称
    lastLoginAt: timestamp('last_login_at', { withTimezone: true }), // 最后登录时间
    loginCount: integer('login_count').notNull().default(0), // 登录次数
    // todayAdWatchCount: integer('today_ad_watch_count').notNull().default(0), // 今日广告观看次数
    // lastAdWatchAt: timestamp('last_ad_watch_at', { withTimezone: true }),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  },
  table => [
    // 索引优化
    index('idx_user_profiles_uid').on(table.uid),
    index('idx_user_profiles_user_name').on(table.userName),
    index('idx_user_profiles_last_login').on(table.lastLoginAt),
  ],
)

export const userAdInfo = pgTable(
  'user_ad',
  {
    userId: uuid('user_id').primaryKey().references(() => userProfiles.id),
    uid: varchar('uid', { length: 50 }).notNull().unique(),
    todayAdWatchCount: integer('today_ad_watch_count').notNull().default(0),
    lastAdWatchAt: timestamp('last_ad_watch_at', { withTimezone: true }),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  },
  table => [
    // 索引优化
    index('idx_user_ad_last_watch_at').on(table.lastAdWatchAt),
  ],
)

// 游戏存档表 - 支持大JSON数据和分区优化
export const gameArchives = pgTable(
  'game_archives',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: varchar('user_id', { length: 255 }).notNull(), // 用户ID
    gameId: varchar('game_id', { length: 100 }).notNull(), // 游戏ID
    archiveData: jsonb('archive_data').notNull(), // 存档JSON数据
    version: integer('version').notNull().default(1), // 存档版本号
    lastPlayed: timestamp('last_played', { withTimezone: true }), // 最后游戏时间
    userCreatedAt: timestamp('user_created_at', { withTimezone: true }).notNull(), // 用户创建时间（分区键）
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  },
  table => [
    // 分区优化索引 - 包含分区键的复合索引
    index('idx_game_archives_user_partition').on(table.userCreatedAt, table.userId, table.gameId),
    index('idx_game_archives_user_game_partition').on(table.userCreatedAt, table.userId, table.gameId, table.lastPlayed),
    index('idx_game_archives_game_partition').on(table.userCreatedAt, table.gameId, table.updatedAt),

    // 传统索引（用于跨分区查询）
    index('idx_game_archives_user_game').on(table.userId, table.gameId),
    index('idx_game_archives_user_created').on(table.userCreatedAt),
  ],
)

// 特权购买记录表（包含试用和折扣记录）
export const privilegeRecords = pgTable(
  'privilege_records',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: varchar('user_id', { length: 255 }).notNull(),
    gameId: varchar('game_id', { length: 100 }).notNull(),
    rewardType: varchar('type', { length: 20 }).notNull(), // 'monthly' | 'weekly' | "trial" | "discount"
    status: varchar('status', { length: 20 }).notNull().default('active'), // 'active' | 'expired' | 'cancelled'
    purchasePrice: decimal('purchase_price', { precision: 10, scale: 2 }).default('0'), // 购买价格（试用和折扣为0或空）
    originalPrice: decimal('original_price', { precision: 10, scale: 2 }).default('0'), // 原价
    startDate: timestamp('start_date', { withTimezone: true }).notNull(), // 开始日期
    endDate: timestamp('end_date', { withTimezone: true }).notNull(), // 结束日期
    purchaseSource: varchar('purchase_source', { length: 50 }).default(''), // 支付平台
    transactionId: varchar('transaction_id', { length: 255 }).default(''), // 支付平台交易ID
    claimDates: jsonb('claim_dates').default('[]'), // 奖励领取日期数组 ['2024-01-01', '2024-01-02']
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  },
  table => [
    index('idx_privilege_records_user').on(table.userId),
    index('idx_privilege_records_user_type').on(table.userId, table.rewardType),
    index('idx_privilege_records_status').on(table.status),
    index('idx_privilege_records_end_date').on(table.endDate),
  ],
)

export const mailData = pgTable('mail_data', {
  mailId: uuid('mailId').primaryKey().defaultRandom(),
  userId: varchar('user_id', { length: 255 }).notNull(),
  isRead: boolean('is_read').default(false),
  isClaimed: boolean('is_claimed').default(false),
  expiredTime: timestamp('expired_time', { withTimezone: true }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
  title: varchar('title', { length: 100 }).default(''),
  rewards: jsonb('rewards').default('{}'),
  content: text('content').default(''),
}, table => [
  index('idx_user_mails_on_user_id').on(table.userId),
  index('idx_user_mails_on_expired_time').on(table.expiredTime),
])
