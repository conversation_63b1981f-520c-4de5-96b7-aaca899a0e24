import z from 'zod'
import { idSchema } from './user'
import 'zod-openapi'

interface CardStatus {
  active: boolean
  claimed: boolean
  days: number
}

export interface PrivilegeStatus {
  userId: string
  monthly: CardStatus
  weekly: CardStatus
  trial: CardStatus
  todayAdWatchCount: number
  lastUpdated: Date | null
}

export const userIdSchema = z.object({
  userId: idSchema,
})

export const purchaseSchema = z.object({
  userId: idSchema,
  rewardType: z.enum(['trial', 'monthly', 'weekly']).meta({
    example: 'monthly',
    description: '特权卡类型',
  }),
  gameId: z.string().min(1, '游戏ID不能为空').meta({
    example: '1:980900739219:android:40c492488c1236409af566r',
    description: '游戏ID',
  }),
})
export const rewardType = {
  monthly: 'monthly',
  weekly: 'weekly',
  trial: 'trial',
} as const
export const rewardClaimSchema = z.object({
  userId: idSchema,
  rewardType: purchaseSchema.shape.rewardType,
})

export const privilegeStatusResponse = z.object({
  success: z.boolean(),
  data: z.object({
    userId: idSchema,
    hasMonthlyCard: z.boolean(),
    hasWeeklyCard: z.boolean(),
    monthlyCardEndDate: z.string(),
    monthlyTrialEndTime: z.string(),
    weeklyCardEndDate: z.string(),
    lastUpdated: z.string(),
  }),
})

export const adWatchResponse = z.object({
  success: z.boolean(),
  data: z.object({
    userId: z.string(),
    adWatchCount: z.number(),
    lastWatchTime: z.string(),
  }),
})

export const trialResponse = z.object({
  success: z.boolean(),
  data: z.object({
    id: z.string(),
    userId: z.string(),
    startTime: z.string(),
    endTime: z.string(),
    createdAt: z.string(),
  }),
})

export const purchaseResponse = z.object({
  success: z.boolean(),
  data: z.object({
    id: z.string(),
    userId: z.string(),
    gameId: z.string(),
    rewardType: z.string(),
    startDate: z.string(),
    endDate: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
  }),
})

export const rewardClaimResponse = z.object({
  success: z.boolean(),
  data: z.object({
    id: z.string(),
    userId: z.string(),
    rewardType: z.string(),
    rewardDate: z.string(),
    claimedAt: z.string(),
    createdAt: z.string(),
  }),
})

export const configResponse = z.object({
  success: z.boolean(),
  data: z.any(),
})
