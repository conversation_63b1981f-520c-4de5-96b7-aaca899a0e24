import { <PERSON>alar } from '@scalar/hono-api-reference'
import { openAPISpecs } from 'hono-openapi'
import { cors } from 'hono/cors'
import createApp from '@/lib/create-app'
import { jwtAuth } from './lib/jwt'
import gameArchivesWS from './routes/game-archives-ws'
import globalRouter from './routes/global'
import health from './routes/health'
import mail from './routes/mail'
import privilegeRecords from './routes/privilege-cards'
import queueAdmin from './routes/queue-admin'
import record from './routes/record'
import user from './routes/user'
import { getSchedulerStatus } from './scheduler'
import { getTime } from './utils/time'
import 'dotenv/config'

const app = createApp()
app.use('/*', cors())

// 健康检查路由
app.route('/', health)
app.route('/api/global', globalRouter)
app.route('/api/record', record)

// 游戏存档 WebSocket 路由
app.route('/ws', gameArchivesWS)
app.get(
  '/openapi',
  openAPISpecs(app, {
    documentation: {
      info: {
        title: 'nqhy-inter',
        version: '1.0.0',
        description: 'API for greeting users',
      },
      servers: [
        {
          url: 'https://************:3001',
          description: 'Local server',
        },
      ],
      components: {
        securitySchemes: {
          httpBearer: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      security: [
        { httpBearer: [], apiKeyHeader: [] }, // 全局开启，所有路由默认需要 Bearer
      ],
    },
  }),
)
process.env.NODE_ENV === 'development' && app.get(
  '/docs',
  Scalar({
    theme: 'purple',
    url: '/openapi',
    hideModels: false,
    forceDarkModeState: 'dark',
    pageTitle: 'NQHY-INTER API',
    defaultHttpClient: {
      targetKey: 'javascript',
      clientKey: 'fetch',
    },
    authentication: {
      preferredSecurityScheme: 'httpBearer',
      apiKey: {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.Qm8vQGhqYXRlc3RfdG9rZW5fZm9yX2RldmVsb3BtZW50',
      },
      httpBearer: {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.Qm8vQGhqYXRlc3RfdG9rZW5fZm9yX2RldmVsb3BtZW50',
      },
    },
    defaultOpenAllTags: true,
  }),
)

// 特权卡 HTTP API 路由
app.route('/api/privilege', privilegeRecords)

// 邮件 HTTP API 路由
app.route('/api/mail', mail)

// 用户信息 HTTP 路由
app.route('/api/user', user)

// 根路径处理
app.get('/', (c) => {
  return c.json({
    name: 'NQHY Backend API',
    version: '1.0.0',
    status: 'running',
    timestamp: getTime().toISOString(),
  })
})
// 404处理
app.notFound((c) => {
  return c.json({
    message: 'Not Found',
    timestamp: getTime().toISOString(),
  }, 404)
})

// 队列管理路由
// app.route('/api', queueAdmin)

// sendBatchCards({
//   tplId: '30888',
//   cronExpression: '* 16 * * *',
//   appId: 'tt5e4928f4c142201d02',
//   messageData: [10, 1, 10, 10, 20, 1, 1],
//   meta: {
//     scene: 1,
//     extra: '',
//   },
//   type: 'feed',
// })

// app.post('/check', checkAndExecuteScheduledTasks)

export default app
