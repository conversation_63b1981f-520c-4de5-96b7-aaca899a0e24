import { Hono } from 'hono'
import { describeRoute } from 'hono-openapi'
import { PRIVILEGE_CONFIG } from '@/config'
import { getSchedulerStatus } from '@/scheduler'
import { globalResponse } from '@/types'
import { getTime } from '@/utils/time'

const router = new Hono()
const tags = ['全局']
// 调度器状态路由
router.post('/scheduler/status', globalResponse({
  description: '获取调度器状态',
  tags,
}), (c) => {
  try {
    const status = getSchedulerStatus()
    return c.json({
      success: true,
      data: status,
      timestamp: getTime().toISOString(),
    })
  }
  catch (error) {
    return c.json({
      success: false,
      error: error.message,
      timestamp: getTime().toISOString(),
    }, 500)
  }
})

router.post('/getConfig', globalResponse({
  description: '获取配置',
  tags,
}), async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        ...PRIVILEGE_CONFIG,
      },
    })
  }
  catch (error) {
    console.error('获取每日奖励失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

export default router
