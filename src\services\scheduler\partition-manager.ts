import type { PartitionConfig } from '@/scheduler/scheduled-tasks-db'
import { sql } from 'drizzle-orm'
import { getDb } from '@/db'
import logger from '@/lib/logger'
import {
  generatePartitionName,
  getPartitionConfigs,
  getPartitionsToCleanup,
  getPartitionsToCreate,

} from '@/scheduler/scheduled-tasks-db'
import { getTime } from '@/utils/time'

export class PartitionManager {
  /**
   * 创建下个月的分区表（基于配置）
   */
  async createNextMonthPartitions(): Promise<void> {
    try {
      const db = await getDb()
      const partitionsToCreate = getPartitionsToCreate()

      logger.info('开始创建分区表', {
        totalPartitions: partitionsToCreate.length,
        tables: [...new Set(partitionsToCreate.map(p => p.config.tableName))],
      })

      for (const { config, year, month } of partitionsToCreate) {
        await this.createPartition(db, config, year, month)
      }

      logger.info('分区表创建完成', {
        createdPartitions: partitionsToCreate.length,
      })
    }
    catch (error) {
      logger.error('创建分区表失败', { message: error.message })
      throw error
    }
  }

  /**
   * 创建单个分区表
   */
  private async createPartition(db: any, config: PartitionConfig, year: number, month: number): Promise<void> {
    try {
      const partitionName = generatePartitionName(config.tableName, year, month)
      const monthStr = month.toString().padStart(2, '0')
      const startDate = `${year}-${monthStr}-01`
      const nextMonth = getTime(new Date(year, month, 1))
      const endDate = nextMonth.toISOString().split('T')[0]

      // 检查分区是否已存在
      const existsResult = await db.execute(sql`
        SELECT 1 FROM pg_class
        WHERE relname = ${partitionName}
      `)

      if (existsResult.length > 0) {
        logger.info('分区已存在，跳过创建', {
          table: config.tableName,
          partition: partitionName,
        })
        return
      }

      // 创建分区表
      await db.execute(sql`
        CREATE TABLE ${sql.identifier(partitionName)} PARTITION OF ${sql.identifier(config.tableName)}
        FOR VALUES FROM (${startDate}) TO (${endDate})
      `)

      logger.info('分区创建成功', {
        table: config.tableName,
        partition: partitionName,
        dateRange: `${startDate} to ${endDate}`,
      })
    }
    catch (error) {
      logger.error('创建分区失败', {
        table: config.tableName,
        year,
        month,
        message: error.message,
      })
      throw error
    }
  }

  /**
   * 创建 eventData 月度分区（保留兼容性）
   */
  private async createEventDataPartition(db: any, year: number, month: string): Promise<void> {
    try {
      const partitionName = `event_data_${year}_${month}`
      const startDate = `${year}-${month}-01`
      const endDate = getTime(new Date(year, Number.parseInt(month), 1)).toISOString().split('T')[0]

      // 检查分区是否已存在
      const existsResult = await db.execute(sql`
        SELECT 1 FROM pg_class 
        WHERE relname = ${partitionName}
      `)

      if (existsResult.length > 0) {
        logger.info('eventData 分区已存在，跳过创建', { partitionName })
        return
      }

      // 创建分区表
      await db.execute(sql`
        CREATE TABLE ${sql.identifier(partitionName)} PARTITION OF event_data
        FOR VALUES FROM (${startDate}) TO (${endDate})
      `)

      // 创建分区索引
      await db.execute(sql`
        CREATE INDEX ${sql.identifier(`idx_${partitionName}_event_time`)} 
        ON ${sql.identifier(partitionName)} (event_time)
      `)

      await db.execute(sql`
        CREATE INDEX ${sql.identifier(`idx_${partitionName}_user_id`)} 
        ON ${sql.identifier(partitionName)} (user_id)
      `)

      await db.execute(sql`
        CREATE INDEX ${sql.identifier(`idx_${partitionName}_event_type`)} 
        ON ${sql.identifier(partitionName)} (event_type)
      `)

      logger.info('eventData 分区创建成功', { partitionName, startDate, endDate })
    }
    catch (error) {
      logger.error('创建 eventData 分区失败', {
        message: error.message,
        year,
        month,
      })
      throw error
    }
  }

  /**
   * 创建 gameArchives 年度分区（如果需要）
   */
  private async createGameArchivesPartition(db: any, year: number, month: string): Promise<void> {
    try {
      // 只在1月份创建新年度分区
      if (month !== '01') {
        logger.debug('非1月份，跳过 gameArchives 年度分区创建', { year, month })
        return
      }

      const partitionName = `game_archives_${year}`
      const startDate = `${year}-01-01`
      const endDate = `${year + 1}-01-01`

      // 检查分区是否已存在
      const existsResult = await db.execute(sql`
        SELECT 1 FROM pg_class 
        WHERE relname = ${partitionName}
      `)

      if (existsResult.length > 0) {
        logger.info('gameArchives 分区已存在，跳过创建', { partitionName })
        return
      }

      // 创建分区表
      await db.execute(sql`
        CREATE TABLE ${sql.identifier(partitionName)} PARTITION OF game_archives
        FOR VALUES FROM (${startDate}) TO (${endDate})
      `)

      // 创建分区索引
      await db.execute(sql`
        CREATE INDEX ${sql.identifier(`idx_${partitionName}_user_game`)} 
        ON ${sql.identifier(partitionName)} (user_id, game_id)
      `)

      await db.execute(sql`
        CREATE INDEX ${sql.identifier(`idx_${partitionName}_last_played`)} 
        ON ${sql.identifier(partitionName)} (user_id, last_played)
      `)

      await db.execute(sql`
        CREATE INDEX ${sql.identifier(`idx_${partitionName}_user_created`)} 
        ON ${sql.identifier(partitionName)} (user_created_at)
      `)

      logger.info('gameArchives 分区创建成功', { partitionName, startDate, endDate })
    }
    catch (error) {
      logger.error('创建 gameArchives 分区失败', {
        message: error.message,
        year,
        month,
      })
      throw error
    }
  }

  /**
   * 清理旧分区（可选）
   */
  async cleanupOldPartitions(retentionMonths: number = 36): Promise<void> {
    try {
      const db = await getDb()
      const cutoffDate = getTime()
      cutoffDate.setMonth(cutoffDate.getMonth() - retentionMonths)

      const cutoffYear = cutoffDate.getFullYear()
      const cutoffMonth = String(cutoffDate.getMonth() + 1).padStart(2, '0')

      logger.info('开始清理旧分区', { cutoffYear, cutoffMonth, retentionMonths })

      // 清理 eventData 旧分区
      await this.cleanupEventDataPartitions(db, cutoffYear, cutoffMonth)

      logger.info('旧分区清理完成')
    }
    catch (error) {
      logger.error('清理旧分区失败', { message: error.message })
      throw error
    }
  }

  /**
   * 清理 eventData 旧分区
   */
  private async cleanupEventDataPartitions(db: any, cutoffYear: number, cutoffMonth: string): Promise<void> {
    try {
      // 查找需要清理的分区
      const partitionsResult = await db.execute(sql`
        SELECT tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'event_data_%' 
          AND tablename < ${`event_data_${cutoffYear}_${cutoffMonth}`}
      `)

      for (const partition of partitionsResult) {
        const partitionName = partition.tablename

        // 删除分区表（数据会被删除）
        await db.execute(sql`DROP TABLE IF EXISTS ${sql.identifier(partitionName)}`)

        logger.info('已删除 eventData 旧分区', { partitionName })
      }
    }
    catch (error) {
      logger.error('清理 eventData 旧分区失败', { message: error.message })
    }
  }

  /**
   * 获取分区统计信息
   */
  async getPartitionStats(): Promise<any> {
    try {
      const db = await getDb()

      // 获取 eventData 分区统计
      const eventDataStats = await db.execute(sql`
        SELECT 
          tablename,
          pg_size_pretty(pg_total_relation_size(tablename)) as size,
          (SELECT reltuples::bigint FROM pg_class WHERE relname = tablename) as estimated_rows
        FROM pg_tables 
        WHERE tablename LIKE 'event_data_%'
        ORDER BY tablename
      `)

      // 获取 gameArchives 分区统计
      const gameArchivesStats = await db.execute(sql`
        SELECT 
          tablename,
          pg_size_pretty(pg_total_relation_size(tablename)) as size,
          (SELECT reltuples::bigint FROM pg_class WHERE relname = tablename) as estimated_rows
        FROM pg_tables 
        WHERE tablename LIKE 'game_archives_%'
        ORDER BY tablename
      `)

      return {
        eventData: eventDataStats,
        gameArchives: gameArchivesStats,
        generatedAt: getTime().toISOString(),
      }
    }
    catch (error) {
      logger.error('获取分区统计失败', { message: error.message })
      throw error
    }
  }

  /**
   * 获取基于配置的分区统计信息
   */
  async getPartitionStatsByConfig(): Promise<any> {
    try {
      const db = await getDb()
      const configs = getPartitionConfigs()
      const stats: any = {
        tables: {},
        summary: {
          totalTables: configs.length,
          totalPartitions: 0,
          totalRows: 0,
          totalSize: '0 MB',
        },
        generatedAt: getTime().toISOString(),
      }

      for (const config of configs) {
        try {
          // 获取表的所有分区
          const partitionsResult = await db.execute(sql`
            SELECT
              schemaname,
              tablename,
              pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
              pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
            FROM pg_tables
            WHERE tablename LIKE ${`${config.tableName}_%`}
            ORDER BY tablename
          `)

          // 获取主表统计
          const mainTableResult = await db.execute(sql`
            SELECT
              COUNT(*) as row_count,
              pg_size_pretty(pg_total_relation_size(${config.tableName})) as size,
              pg_total_relation_size(${config.tableName}) as size_bytes
            FROM ${sql.identifier(config.tableName)}
          `)

          const partitions = partitionsResult || []
          const mainTableStats = mainTableResult[0] || { row_count: 0, size: '0 bytes', size_bytes: 0 }

          stats.tables[config.tableName] = {
            description: config.description,
            partitionType: config.partitionType,
            retentionMonths: config.retentionMonths,
            totalRows: Number(mainTableStats.row_count),
            totalSize: mainTableStats.size,
            totalSizeBytes: Number(mainTableStats.size_bytes),
            partitionCount: partitions.length,
            partitions: partitions.map((p: any) => ({
              name: p.tablename,
              size: p.size,
              sizeBytes: Number(p.size_bytes),
            })),
          }

          stats.summary.totalPartitions += partitions.length
          stats.summary.totalRows += Number(mainTableStats.row_count)
        }
        catch (tableError) {
          logger.warn('获取表统计失败', {
            table: config.tableName,
            error: tableError.message,
          })

          stats.tables[config.tableName] = {
            description: config.description,
            error: tableError.message,
            partitionCount: 0,
            totalRows: 0,
            totalSize: '0 bytes',
          }
        }
      }

      logger.info('分区统计生成完成', {
        tablesCount: Object.keys(stats.tables).length,
        totalPartitions: stats.summary.totalPartitions,
      })

      return stats
    }
    catch (error) {
      logger.error('获取基于配置的分区统计失败', { message: error.message })
      throw error
    }
  }
}

// 导出单例实例
export const partitionManager = new PartitionManager()
