import type { Context, Next } from 'hono'
import type { SignOptions } from 'jsonwebtoken'
import jwt from 'jsonwebtoken'

const JWT_SECRET = 'yhqn'

export interface JWTPayload {
  id: number
  uid: string
  userName: string
  isGuest: boolean
  isNewUser: boolean
  isPreReg: boolean
  openType?: string
}

/**
 * 生成 JWT token
 * @param payload JWT 载荷数据
 * @param expiresIn 过期时间，默认 7 天
 * @returns JWT token 字符串
 */
export function generateJWT(payload: JWTPayload/* , expiresIn: string = "7d" */): string {
  return jwt.sign(payload, JWT_SECRET/* , { expiresIn } as SignOptions */)
}

/**
 * 验证并解析 JWT token
 * @param token JWT token 字符串
 * @returns 解析后的载荷数据和可能的新token
 */
export function verifyJWTAuto(token: string): { payload: JWTPayload, newToken?: string } {
  try {
    const payload = jwt.verify(token, JWT_SECRET) as JWTPayload
    return { payload }
  }
  catch (error) {
    // 如果是过期错误，尝试解码并续签
    if (error instanceof jwt.TokenExpiredError) {
      try {
        // 解码过期的 token（不验证签名和过期时间）
        const decoded = jwt.decode(token) as JWTPayload

        if (decoded && decoded.id && decoded.uid) {
          // 创建新的 payload（移除 iat 和 exp）
          const { iat, exp, ...payloadData } = decoded as any

          // 生成新的 token
          const newToken = generateJWT(payloadData as JWTPayload)

          return {
            payload: payloadData as JWTPayload,
            newToken,
          }
        }
      }
      catch (decodeError) {
        // 如果解码也失败，抛出原始错误
        throw error
      }
    }

    // 其他错误直接抛出
    throw error
  }
}

/**
 * 简单验证 JWT token（不自动续签）
 * @param token JWT token 字符串
 * @returns 解析后的载荷数据
 */
export function verifyJWT(token: string): JWTPayload {
  return jwt.verify(token, JWT_SECRET) as JWTPayload
}

/**
 * 解码 JWT token（不验证签名）
 * @param token JWT token 字符串
 * @returns 解析后的载荷数据
 */
export function decodeJWT(token: string): JWTPayload | null {
  const decoded = jwt.decode(token)
  return decoded as JWTPayload | null
}

/**
 * JWT 身份验证中间件
 * 从 Authorization header 或 AccessToken header 中提取 JWT token 并验证
 * 验证成功后将用户信息存储在 c.get('user') 中
 */
export function jwtAuth() {
  return async (c: Context, next: Next) => {
    try {
      // if (process.env.NODE_ENV === 'development') {
      //   await next()
      // }
      // 从多个可能的 header 中获取 token
      let token = c.req.header('Authorization')
      if (token && token.startsWith('Bearer ')) {
        token = token.slice(7) // 移除 'Bearer ' 前缀
      }
      else {
        token = c.req.header('AccessToken') || c.req.header('access-token')
      }

      if (!token) {
        return c.json({
          success: false,
          message: '缺少访问令牌',
        }, 401)
      }

      const payload = verifyJWT(token)
      c.set('user', payload)

      await next()
    }
    catch (error) {
      return c.json({
        success: false,
        message: `无效的访问令牌${error}`,
      }, 401)
    }
  }
}

/**
 * 可选的 JWT 身份验证中间件
 * 如果提供了 token 则验证，如果没有提供则继续执行（不报错）
 * 验证成功后将用户信息存储在 c.get('user') 中
 */
export function optionalJwtAuth() {
  return async (c: Context, next: Next) => {
    try {
      // 从多个可能的 header 中获取 token
      let token = c.req.header('Authorization')
      if (token && token.startsWith('Bearer ')) {
        token = token.slice(7) // 移除 'Bearer ' 前缀
      }
      else {
        // 如果没有 Authorization header，尝试从 AccessToken header 获取
        token = c.req.header('AccessToken') || c.req.header('access-token')
      }

      if (token) {
        // 如果提供了 token，则验证它
        const { payload, newToken } = verifyJWT(token)
        c.set('user', payload)

        // 如果有新 token（续签），在响应头中返回
        if (newToken) {
          c.header('X-New-Token', newToken)
          c.header('X-Token-Renewed', 'true')
        }
      }

      await next()
    }
    catch (error) {
      // 如果 token 无效，继续执行但不设置用户信息
      await next()
    }
  }
}
