import { loadScheduledTasksFromDB } from '@/scheduler/scheduled-tasks-db'
import { rewardEnum } from '@/types'
import { getTime, TIMEZONE } from '@/utils/time'
import 'dotenv/config'
// 定时任务配置
export interface ScheduledMessageTask {
  tplId: string
  messageData: any[]
  max?: number
  page?: string
  cronExpression: string // cron表达式，如 '0 9 * * *' (每天9点)
  intervalHours?: number // 执行间隔时间（小时），默认24小时
  enabled: boolean
  platform: string
  appId: string
  type?: string // 任务类型：recurring(循环) 或 single(单次)
  meta?: Record<string, any> // 元数据，JSON格式，包含任务的额外配置信息
}

export const REWARD_CONFIG = {
  monthly: {
    daily: { [rewardEnum.blindBox]: 20, [rewardEnum.adTicket]: 20, [rewardEnum.energy]: 10 },
    discountPercent: 60,
  },
  trial: { daily: { [rewardEnum.blindBox]: 10, [rewardEnum.adTicket]: 10, [rewardEnum.energy]: 5 } },
  weekly: {
    daily: { [rewardEnum.blindBox]: 10, [rewardEnum.adTicket]: 10, [rewardEnum.energy]: 5 },
  },
} as const
// 广告观看阈值
export const AD_THRESHOLDS = {
  trial: 5,
  monthlyDiscount: 10,
} as const

export const PRIVILEGE_CONFIG = {
  REWARD_CONFIG,
  AD_THRESHOLDS,
  EMAIL: {
    MAX: 20,
  },
  GLOBAL: {
    DATE: () => getTime(),
    TIME_ZONE_OFFSET: () => getTime().getTimezoneOffset(),
  },
}
// 缓存的任务配置
let cachedMessageTasks: ScheduledMessageTask[] = []
let lastLoadTime = 0
const CACHE_DURATION = 30 * 60 * 1000 // 5分钟缓存

/**
 * 获取定时任务配置（带缓存）
 */
export async function getMessageTasks(): Promise<ScheduledMessageTask[]> {
  const now = Date.now()

  // 如果缓存有效，直接返回
  if (cachedMessageTasks.length > 0 && (now - lastLoadTime) < CACHE_DURATION) {
    return cachedMessageTasks
  }

  try {
    // 从数据库加载最新配置
    cachedMessageTasks = await loadScheduledTasksFromDB()
    lastLoadTime = now

    return cachedMessageTasks
  }
  catch (error) {
    console.error('加载定时任务配置失败，使用缓存配置:', error.message)

    // 如果数据库加载失败且没有缓存，返回空数组
    return cachedMessageTasks
  }
}

/**
 * 清除缓存，强制重新加载
 */
export function clearMessageTasksCache(): void {
  cachedMessageTasks = []
  lastLoadTime = 0
}

/**
 * 同步方式获取缓存的任务配置（用于向后兼容）
 * 注意：这个方法可能返回空数组，建议使用 getMessageTasks()
 */
export const messageTasks: ScheduledMessageTask[] = []

// 初始化时加载一次配置
getMessageTasks().then((tasks) => {
  messageTasks.splice(0, messageTasks.length, ...tasks)
}).catch((error) => {
  console.error('初始化定时任务配置失败:', error.message)
})
