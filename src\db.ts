import { drizzle } from 'drizzle-orm/node-postgres'
import { Pool } from 'pg'
// import * as schema from './schema'

let dbInstance: any
let readDbInstance: any

export async function getDb() {
  if (!dbInstance) {
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 100, // 最大连接数
      min: 10, // 最小连接数
      idleTimeoutMillis: 60000, // 空闲超时
      connectionTimeoutMillis: 10000, // 连接超时
      keepAlive: true, // 保持连接活跃
      keepAliveInitialDelayMillis: 0, // 保活延迟
      allowExitOnIdle: false, // 不允许在空闲时退出
    })

    dbInstance = drizzle(pool)
  }

  return dbInstance
}

// 只读数据库连接（连接到副本）
export async function getReadDb() {
  if (!readDbInstance) {
    // 如果有副本连接字符串，使用副本；否则使用主库
    const replicaUrl = process.env.DATABASE_URL

    const readPool = new Pool({
      connectionString: replicaUrl,
      max: 50, // 读操作连接数较少
      min: 5,
      idleTimeoutMillis: 60000,
      connectionTimeoutMillis: 10000,
      keepAlive: true,
      keepAliveInitialDelayMillis: 0,
      allowExitOnIdle: false,
    })

    readDbInstance = drizzle(readPool)
  }

  return readDbInstance
}
