import { schedulerLogger } from '@/lib/logger'
import { schedulerManager } from './scheduler-manager'
import { createTaskExecutors } from './task-executors'

/**
 * 统一的调度器入口
 * 负责初始化和管理所有定时任务
 */
export class UnifiedScheduler {
  private initialized = false

  /**
   * 初始化调度器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      schedulerLogger.warn('调度器已初始化')
      return
    }

    try {
      schedulerLogger.info('开始初始化统一调度器')

      // 创建任务执行器
      const executors = createTaskExecutors()

      // 注册特权卡相关任务
      schedulerManager.registerTask({
        name: 'sendUnclaimedRewards',
        cronExpression: '0 * * * *', // 每小时执行
        executor: executors.sendUnclaimedRewards,
        lockTTL: 1800, // 30分钟锁
      })

      schedulerManager.registerTask({
        name: 'resetDailyStatus',
        cronExpression: '0 0 * * *', // 每日0点执行
        executor: executors.resetDailyStatus,
        lockTTL: 3600, // 1小时锁
      })

      schedulerManager.registerTask({
        name: 'cleanupExpiredData',
        cronExpression: '0 0 * * 0', // 每周日0点执行
        executor: executors.cleanupExpiredData,
        lockTTL: 7200, // 2小时锁
      })

      // 注册分区管理任务
      schedulerManager.registerTask({
        name: 'createPartitions',
        cronExpression: '0 2 25 * *', // 每月25日凌晨2点执行
        executor: executors.createPartitions,
        lockTTL: 3600, // 1小时锁
      })

      schedulerManager.registerTask({
        name: 'cleanupPartitions',
        cronExpression: '0 3 1 * *', // 每月1日凌晨3点执行
        executor: executors.cleanupPartitions,
        lockTTL: 7200, // 2小时锁
      })

      schedulerManager.registerTask({
        name: 'partitionStats',
        cronExpression: '0 4 * * 0', // 每周日凌晨4点执行
        executor: executors.partitionStats,
        lockTTL: 1800, // 30分钟锁
      })

      // 注册消息任务检查器
      schedulerManager.registerTask({
        name: 'checkMessageTasks',
        cronExpression: '0 * * * * *', // 每分钟执行
        executor: executors.checkMessageTasks,
        lockTTL: 300, // 5分钟锁
      })

      this.initialized = true
      schedulerLogger.info('统一调度器初始化完成')

    } catch (error) {
      schedulerLogger.error('调度器初始化失败', { error: error.message })
      throw error
    }
  }

  /**
   * 启动调度器
   */
  async start(): Promise<void> {
    // 检查是否启用定时任务
    if (process.env.SCHEDULER_ENABLED === 'false') {
      schedulerLogger.info('定时任务调度器已禁用')
      return
    }

    if (!this.initialized) {
      await this.initialize()
    }

    schedulerManager.start()
    schedulerLogger.info('统一调度器已启动')
  }

  /**
   * 停止调度器
   */
  stop(): void {
    schedulerManager.stop()
    schedulerLogger.info('统一调度器已停止')
  }

  /**
   * 获取调度器状态
   */
  getStatus(): any {
    return {
      initialized: this.initialized,
      ...schedulerManager.getTaskStatus()
    }
  }

  /**
   * 手动执行指定任务（用于测试）
   */
  async executeTask(taskName: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('调度器未初始化')
    }

    const executors = createTaskExecutors()
    const executor = (executors as any)[taskName]
    
    if (!executor) {
      throw new Error(`未找到任务执行器: ${taskName}`)
    }

    schedulerLogger.info('手动执行任务', { taskName })
    await executor.execute()
    schedulerLogger.info('手动执行任务完成', { taskName })
  }
}

// 导出单例实例
export const unifiedScheduler = new UnifiedScheduler()

/**
 * 启动统一调度器的便捷函数
 */
export async function startUnifiedScheduler(): Promise<void> {
  await unifiedScheduler.start()
}

/**
 * 停止统一调度器的便捷函数
 */
export function stopUnifiedScheduler(): void {
  unifiedScheduler.stop()
}

/**
 * 获取调度器状态的便捷函数
 */
export function getSchedulerStatus(): any {
  return unifiedScheduler.getStatus()
}

// 兼容性导出 - 保持向后兼容
export { schedulerManager } from './scheduler-manager'
export * from './task-executors'
