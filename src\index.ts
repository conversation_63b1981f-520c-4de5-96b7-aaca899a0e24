import { serve } from '@hono/node-server'
import { createServer as createHttpsServer } from 'https'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

import logger  from '@/lib/logger'
import app from './app'
import { initKafka } from './services/scheduler/kafka-service'
import { injectWebSocket } from './routes/game-archives-ws'
import { startUnifiedScheduler } from './scheduler'

const port = Number(process.env.PORT) || 3000
const httpsEnabled = process.env.HTTPS_ENABLED !== 'false' 

logger.info('启动应用服务器', {
  port,
  https: httpsEnabled,
  nodeEnv: process.env.NODE_ENV,
  logLevel: process.env.LOG_LEVEL,
})

// 延迟启动统一调度器，确保服务器先启动
setTimeout(async () => {
  try {
    const schedulerMode = process.env.SCHEDULER_MODE || 'direct'

    if(schedulerMode === "kafka"){
      logger.info('使用Kafka模式的定时任务调度器')
      initKafka()
    } else if(schedulerMode === "direct"){
      logger.info('使用统一调度器模式')
    } else if(schedulerMode === "disabled"){
      logger.info('定时任务调度器已禁用')
      return
    } else {
      logger.warn(`未知的调度器模式: ${schedulerMode}，禁用调度器`)
      return
    }

    // 启动统一调度器
    await startUnifiedScheduler()
    logger.info('统一调度器启动成功')
  } catch (error) {
    logger.error('启动调度器失败', { error: error.message })
  }
}, 3000)

// 处理未捕获的异常
// process.on('uncaughtException', (error) => {
//   logger.fatal('未捕获的异常', { error: error.message, stack: error.stack })
//   process.exit(1)
// })

// process.on('unhandledRejection', (reason, promise) => {
//   logger.fatal('未处理的Promise拒绝', { reason, promise })
//   process.exit(1)
// })


if (httpsEnabled) {
  try {
    const certPaths = [
      join(process.cwd(), 'certs', 'server.crt'),
      join(process.cwd(), 'certs', 'server.pem'),
      join(process.cwd(), 'certs', 'cert.pem'),
      join(process.cwd(), 'certs', 'certificate.pem')
    ]

    const keyPaths = [
      join(process.cwd(), 'certs', 'server.key'),
      join(process.cwd(), 'certs', 'private.key'),
      join(process.cwd(), 'certs', 'key.pem'),
      join(process.cwd(), 'certs', 'private.pem')
    ]

    // 查找存在的证书文件
    const certPath = certPaths.find(path => existsSync(path))
    const keyPath = keyPaths.find(path => existsSync(path))

    if (!certPath) {
      throw new Error('SSL证书文件不存在，请确保以下任一文件存在: ' + certPaths.map(p => p.replace(process.cwd(), '.')).join(', '))
    }

    if (!keyPath) {
      throw new Error('SSL私钥文件不存在，请确保以下任一文件存在: ' + keyPaths.map(p => p.replace(process.cwd(), '.')).join(', '))
    }

    logger.info('找到SSL证书文件', {
      certFile: certPath.replace(process.cwd(), '.'),
      keyFile: keyPath.replace(process.cwd(), '.')
    })

    // 读取证书文件
    logger.info('读取SSL证书文件', { certPath, keyPath })

    let certContent, keyContent
    try {
      certContent = readFileSync(certPath, 'utf8')
      keyContent = readFileSync(keyPath, 'utf8')

      logger.info('证书文件读取成功', {
        certLength: certContent.length,
        keyLength: keyContent.length,
        certStart: certContent.substring(0, 50) + '...',
        keyStart: keyContent.substring(0, 50) + '...'
      })
    } catch (readError) {
      throw new Error(`读取证书文件失败: ${readError.message}`)
    }

    const httpsOptions = {
      cert: certContent,
      key: keyContent,
    }

    logger.info('创建HTTPS服务器', { httpsOptionsKeys: Object.keys(httpsOptions) })

    const server = serve({
      fetch: app.fetch,
      port,
      createServer: createHttpsServer,
      serverOptions: httpsOptions
    })

    injectWebSocket(server)


    logger.info('HTTPS服务器启动成功', {
      port,
      protocol: 'https',
      url: `https://localhost:${port}`
    })



  } catch (error) {
    logger.error('HTTPS配置失败', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno
    })
    logger.info('回退到HTTP模式')

    const server = serve({
      fetch: app.fetch,
      port,
    })

    injectWebSocket(server)


    logger.info('HTTP服务器启动成功', {
      port,
      protocol: 'http',
      url: `http://localhost:${port}`
    })
  }
} else {
  const server = serve({
    fetch: app.fetch,
    port,
  })

  injectWebSocket(server)


  logger.info('HTTP服务器启动成功', {
    port,
    protocol: 'http',
    url: `http://localhost:${port}`
  })
}
