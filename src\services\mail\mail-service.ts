import { and, desc, eq, gte, inArray, lt, sql } from 'drizzle-orm'
import { PRIVILEGE_CONFIG } from '@/config'
import { getDb, getReadDb } from '@/db'
import { mailData } from '@/schema'
import { getTime } from '@/utils/time'

export class MailService {
  // 1. 查询用户邮件列表（自动删除过期邮件）
  async getUserMails(userId: string, options: {
    limit?: number
    isRead?: boolean
    includeExpired?: boolean
  } = {}) {
    const { isRead, includeExpired = false } = options
    const limit = PRIVILEGE_CONFIG.EMAIL.MAX
    const conditions = [eq(mailData.userId, userId)]

    if (isRead !== undefined) {
      conditions.push(eq(mailData.isRead, isRead))
    }

    if (!includeExpired) {
      conditions.push(gte(mailData.expiredTime, getTime()))
    }

    const db = await getReadDb()

    // 先删除过期邮件
    if (!includeExpired) {
      await this.cleanupExpiredMailsForUser(userId)
    }

    return await db
      .select(
        { mailId: mailData.mailId, isRead: mailData.isRead, isClaimed: mailData.isClaimed, expiredTime: mailData.expiredTime, createdAt: mailData.createdAt, updatedAt: mailData.updatedAt, title: mailData.title },
      )
      .from(mailData)
      .where(and(...conditions))
      .orderBy(desc(mailData.createdAt))
      .limit(limit)
  }

  async readMailById(mailId: string) {
    const db = await getDb()
    const [mail] = await db
      .select()
      .from(mailData)
      .where(eq(mailData.mailId, mailId))
    return mail
  }

  // 2. 标记邮件为已读
  async markMailAsRead(mailId: string) {
    const db = await getDb()

    return await db
      .update(mailData)
      .set({
        isRead: true,
        updatedAt: getTime(),
      })
      .where(eq(mailData.mailId, mailId))
      .returning()
  }

  // 3. 领取邮件奖励
  async claimMailReward(mailId: string) {
    const db = await getDb()

    return await db
      .update(mailData)
      .set({
        isClaimed: true,
        updatedAt: getTime(),
      })
      .where(and(
        eq(mailData.mailId, mailId),
        eq(mailData.isClaimed, false), // 防止重复领取
      ))
      .returning()
  }

  // 4. 批量标记为已读
  async batchMarkAsRead(mailIds: string[]) {
    if (mailIds.length === 0)
      return []

    const db = await getDb()

    return await db
      .update(mailData)
      .set({
        isRead: true,
        updatedAt: getTime(),
      })
      .where(and(
        eq(mailData.isRead, false),
        inArray(mailData.mailId, mailIds),
      ))
      .returning()
  }

  // 5. 批量领取奖励
  async batchClaimRewards(mailIds: string[]) {
    if (mailIds.length === 0)
      return []

    const db = await getDb()

    return await db
      .update(mailData)
      .set({
        isClaimed: true,
        updatedAt: getTime(),
      })
      .where(and(
        inArray(mailData.mailId, mailIds),
      ))
      .returning()
  }

  // 6. 获取未读邮件数量
  async getUnreadCount(userId: string) {
    const db = await getReadDb()

    const result = await db
      .select({ count: sql<number>`count(*)` })
      .from(mailData)
      .where(and(
        eq(mailData.userId, userId),
        // eq(mailData.isRead, false),
        gte(mailData.expiredTime, getTime()),
      ))

    return result[0]?.count || 0
  }

  // 7. 创建新邮件
  async createUserMail(data: {
    userId: string // 改为 string 类型
    expiredTime: Date
    title?: string
    rewards: any // JSON对象
    content: string
  }) {
    const db = await getDb()
    return await db
      .insert(mailData)
      .values({
        userId: data.userId,
        expiredTime: data.expiredTime,
        title: data.title,
        rewards: data.rewards,
        content: data.content,
      })
      .returning()
  }

  // 8. 删除过期邮件
  async cleanupExpiredMails() {
    const db = await getDb()
    return await db
      .delete(mailData)
      .where(lt(mailData.expiredTime, getTime()))
      .returning()
  }

  // 8.1. 删除指定用户的过期邮件
  async cleanupExpiredMailsForUser(userId: string) {
    const db = await getDb()
    return await db
      .delete(mailData)
      .where(and(
        eq(mailData.userId, userId),
        lt(mailData.expiredTime, getTime()),
      ))
      .returning()
  }

  // 9. 根据JSONB字段查询
  async getMailsByCustomField(userId: string, fieldPath: string, value: any) {
    const db = await getReadDb()
    return await db
      .select()
      .from(mailData)
      .where(and(
        eq(mailData.userId, userId),
        sql`${mailData.rewards} #> ${fieldPath} = ${value}`,
      ))
  }
}
