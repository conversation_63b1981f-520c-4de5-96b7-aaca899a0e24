import { jwtAuth, optionalJwtAuth } from '@/lib/jwt'

/**
 * 必需身份验证中间件
 * 用于需要用户登录才能访问的路由
 *
 * 使用方式：
 * - 全局使用：app.use('/api/protected/*', requireAuth())
 * - 单个路由：app.get('/api/user/profile', requireAuth(), handler)
 */
export const requireAuth = jwtAuth

/**
 * 可选身份验证中间件
 * 用于可以匿名访问，但登录用户有额外功能的路由
 *
 * 使用方式：
 * - 全局使用：app.use('/api/public/*', optionalAuth())
 * - 单个路由：app.get('/api/content', optionalAuth(), handler)
 */
export const optionalAuth = optionalJwtAuth

/**
 * 管理员权限检查中间件
 * 需要先使用 requireAuth() 中间件
 */
export function requireAdmin() {
  return async (c: any, next: any) => {
    const user = c.get('user')

    if (!user) {
      return c.json({
        success: false,
        message: '需要身份验证',
      }, 401)
    }

    // 这里可以根据您的业务逻辑判断是否为管理员
    // 例如检查用户角色、权限等
    // if (!user.isAdmin) {
    //   return c.json({
    //     success: false,
    //     message: '需要管理员权限',
    //   }, 403)
    // }

    await next()
  }
}

/**
 * 游客权限检查中间件
 * 只允许非游客用户访问
 */
export function requireNonGuest() {
  return async (c: any, next: any) => {
    const user = c.get('user')

    if (!user) {
      return c.json({
        success: false,
        message: '需要身份验证',
      }, 401)
    }

    if (user.isGuest) {
      return c.json({
        success: false,
        message: '游客用户无法访问此功能',
      }, 403)
    }

    await next()
  }
}
