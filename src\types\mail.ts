import z from 'zod'
import { idSchema } from './user'

// 邮件ID schema
export const mailIdSchema = z.uuid().meta({
  example: '45b4b468-caf8-4581-b682-195a13558c19',
  description: '邮件唯一标识符',
})

// 邮件标题 schema
export const mailTitleSchema = z.string().max(100).optional().meta({
  example: '每日奖励',
  description: '邮件标题',
})

// 邮件内容 schema
export const mailContentSchema = z.record(z.string(), z.any()).meta({
  example: { blindBox: 10, adTicket: 5, energy: 20 },
  description: '邮件内容（JSON格式）',
})

// 过期时间 schema
export const expiredTimeSchema = z.string().meta({
  example: '2026-12-31T23:59:59.000Z',
  description: '邮件过期时间',
})

// 获取邮件列表请求 schema
export const getMailsSchema = z.object({
  isRead: z.boolean().optional().meta({
    example: false,
    description: '是否已读（可选）',
  }),
  includeExpired: z.boolean().optional().default(false).meta({
    example: false,
    description: '是否包含过期邮件',
  }),
})

// 标记邮件为已读请求 schema
export const markMailAsReadSchema = z.object({
  mailId: mailIdSchema,
})

// 领取邮件奖励请求 schema
export const claimMailRewardSchema = z.object({
  mailId: mailIdSchema,
})

// 批量操作请求 schema
export const batchMailOperationSchema = z.object({
  mailIds: z.array(mailIdSchema).min(1, '邮件ID列表不能为空').meta({
    example: ['45b4b468-caf8-4581-b682-195a13558c19'],
    description: '邮件ID列表',
  }),
})

// 创建邮件请求 schema
export const createMailSchema = z.object({
  userId: idSchema,
  title: mailTitleSchema,
  rewards: mailContentSchema,
  expiredTime: expiredTimeSchema,
  content: z.string().meta({
    example: '邮件内容',
    description: '邮件内容',
  }),
})

// 邮件响应 schema
export const mailResponseSchema = z.object({
  mailId: mailIdSchema,
  userId: idSchema,
  title: mailTitleSchema,
  rewards: mailContentSchema,
  content: z.string().meta({
    example: '邮件内容',
    description: '邮件内容',
  }),
  timeAgo: z.number().meta({
    example: 1,
    description: '过期剩余时间（小时）',
  }),
  isRead: z.boolean().meta({
    example: false,
    description: '是否已读',
  }),
  isClaimed: z.boolean().meta({
    example: false,
    description: '是否已领取',
  }),
  expiredTime: z.string().meta({
    example: '2024-12-31T23:59:59.000Z',
    description: '过期时间',
  }),
  createdAt: z.string().meta({
    example: '2024-01-01T00:00:00.000Z',
    description: '创建时间',
  }),
  updatedAt: z.string().meta({
    example: '2024-01-01T00:00:00.000Z',
    description: '更新时间',
  }),
})

// 邮件列表响应 schema
export const mailListResponseSchema = z.object({
  mails: z.array(mailResponseSchema),
  total: z.number().int().min(0).meta({
    example: 10,
    description: '邮件总数',
  }),
  unreadCount: z.number().int().min(0).meta({
    example: 3,
    description: '未读邮件数量',
  }),
})

// 未读邮件数量响应 schema
export const unreadCountResponseSchema = z.object({
  count: z.number().int().min(0).meta({
    example: 5,
    description: '未读邮件数量',
  }),
})

// 导出类型
export type GetMailsRequest = z.infer<typeof getMailsSchema>
export type MarkMailAsReadRequest = z.infer<typeof markMailAsReadSchema>
export type ClaimMailRewardRequest = z.infer<typeof claimMailRewardSchema>
export type BatchMailOperationRequest = z.infer<typeof batchMailOperationSchema>
export type CreateMailRequest = z.infer<typeof createMailSchema>
export type MailResponse = z.infer<typeof mailResponseSchema>
export type MailListResponse = z.infer<typeof mailListResponseSchema>
export type UnreadCountResponse = z.infer<typeof unreadCountResponseSchema>
