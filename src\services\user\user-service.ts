import { and, eq, sql } from 'drizzle-orm'
import { getDb, getReadDb } from '@/db'
import logger from '@/lib/logger'
import { getRedis } from '@/redis'
import { userAdInfo, userProfiles } from '@/schema'
import { getTime, isPreviousDate } from '@/utils/time'
import { PrivilegeCardFunc } from '../privilege/privilege-card-func'

export interface UserProfile {
  id: number
  uid: string
  userName: string
  token?: string
  isGuest: boolean
  isNewUser: boolean
  isPreReg: boolean
  openType?: string
  data: Record<string, any>
  lastLoginAt?: Date
  loginCount: number
  createdAt: Date
  updatedAt: Date
}

export interface CreateUserRequest {
  uid: string
  userName: string
  token?: string
  isGuest?: boolean
  isNewUser?: boolean
  isPreReg?: boolean
  openType?: string
  avatar?: string
  nickname?: string
}

export interface UpdateUserRequest {
  token?: string
  avatar?: string
  nickname?: string
  level?: number
  exp?: number
  coins?: number
  diamonds?: number
}

export class UserService {
  private readonly CACHE_PREFIX = 'user_profile:'
  private readonly CACHE_TTL = 1800 // 30分钟缓存

  /**
   * 生成缓存键
   */
  private getCacheKey(uid: string): string {
    return `${this.CACHE_PREFIX}${uid}`
  }

  /**
   *  登录并获取用户信息，不存在则自动创建
   */
  async getUserProfile(uid: string, createData?: CreateUserRequest): Promise<UserProfile> {
    try {
      const db = await getReadDb()
      const [row] = await db
        .select()
        .from(userProfiles)
        .leftJoin(userAdInfo, eq(userProfiles.id, userAdInfo.userId))
        .where(eq(userProfiles.uid, uid))

      const existingUser = row?.user_profiles
      const adInfo = row?.user_ad

      if (existingUser) {
        setImmediate(() => this.updateLastLogin(uid, createData))
        const jobs = [
          PrivilegeCardFunc.sendUnclaimedRewardsToMail(existingUser.id),
          this.resetDailyStatusByUserId(existingUser.id, adInfo?.lastAdWatchAt),
        ]
        await Promise.allSettled(jobs)
        logger.debug('从数据库获取用户信息', { uid })
        return existingUser
      }

      if (!createData) {
        createData = {
          uid,
          userName: `user_${uid}`,
          isGuest: true,
          isNewUser: true,
          isPreReg: false,
        }
      }

      const newUser = await this.createUser(createData)
      logger.info('自动创建新用户', { uid, userName: newUser.userName })

      return newUser
    }
    catch (error) {
      logger.error('获取用户信息失败', { message: error.message, uid })
      throw error
    }
  }

  /**
   * 创建新用户
   */
  async createUser(request: CreateUserRequest): Promise<UserProfile> {
    try {
      const db = await getDb()
      // const redis = await getRedis()

      const [newUser] = await db.insert(userProfiles).values({
        uid: request.uid,
        userName: request.userName,
        token: request.token,
        isGuest: request.isGuest ?? false,
        isNewUser: request.isNewUser ?? true,
        isPreReg: request.isPreReg ?? false,
        openType: request.openType,
        avatar: request.avatar,
        nickname: request.nickname || request.userName,
        lastLoginAt: getTime(),
        loginCount: 1,
        updatedAt: getTime(),
      }).returning()

      // 初始化用户广告信息表记录
      await db.insert(userAdInfo).values({
        userId: newUser.id,
        uid: newUser.uid,
        todayAdWatchCount: 0,
        createdAt: getTime(),
        updatedAt: getTime(),
      })

      // 缓存新用户信息
      // const cacheKey = this.getCacheKey(request.uid)
      // await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(newUser))

      logger.info('创建用户成功', {
        uid: request.uid,
        userName: request.userName,
        isGuest: request.isGuest,
      })

      return newUser
    }
    catch (error) {
      logger.error('创建用户失败', { message: error.message, uid: request.uid })
      throw error
    }
  }

  /**
   * 更新用户信息
   */
  async updateUser(id: string, request: UpdateUserRequest): Promise<UserProfile | null> {
    try {
      const db = await getDb()
      // const redis = await getRedis()

      const [updatedUser] = await db
        .update(userProfiles)
        .set({
          ...request,
          updatedAt: getTime(),
        })
        .where(eq(userProfiles.id, id))
        .returning()

      if (!updatedUser) {
        return null
      }

      // 更新缓存
      // const cacheKey = this.getCacheKey(uid)
      // await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(updatedUser))

      logger.info('更新用户信息成功', { id })
      return updatedUser
    }
    catch (error) {
      logger.error('更新用户信息失败', { message: error.message, id })
      throw error
    }
  }

  /**
   * 更新最后登录时间
   */
  private async updateLastLogin(uid: string, createData: CreateUserRequest): Promise<void> {
    try {
      const db = await getDb()

      await db
        .update(userProfiles)
        .set({
          lastLoginAt: getTime(),
          loginCount: sql`${userProfiles.loginCount} + 1`,
          isNewUser: false, // 登录后不再是新用户
          updatedAt: getTime(),
          token: createData.token,
        })
        .where(eq(userProfiles.uid, uid))
        .returning()
    }
    catch (error) {
      logger.error('更新最后登录时间失败', { message: error.message, uid })
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 删除用户（软删除，实际保留数据）
   */
  async deleteUser(uid: string): Promise<boolean> {
    try {
      const redis = await getRedis()

      // 只删除缓存，保留数据库数据
      const cacheKey = this.getCacheKey(uid)
      await redis.del(cacheKey)

      logger.info('删除用户缓存', { uid })
      return true
    }
    catch (error) {
      logger.error('删除用户失败', { message: error.message, uid })
      throw error
    }
  }

  /**
   * 批量获取用户信息
   */
  async getUserProfiles(uids: string[]): Promise<UserProfile[]> {
    try {
      const db = await getReadDb()

      const users = await db
        .select()
        .from(userProfiles)
        .where(sql`${userProfiles.uid} = ANY(${uids})`)

      logger.debug('批量获取用户信息', { count: users.length, requestCount: uids.length })
      return users
    }
    catch (error) {
      logger.error('批量获取用户信息失败', { message: error.message, uids })
      throw error
    }
  }

  /**
   * 清除用户缓存
   */
  async clearUserCache(uid: string): Promise<void> {
    try {
      const redis = await getRedis()
      const cacheKey = this.getCacheKey(uid)
      await redis.del(cacheKey)

      logger.debug('清除用户缓存', { uid })
    }
    catch (error) {
      logger.error('清除用户缓存失败', { message: error.message, uid })
    }
  }

  /**
   * 重置每日状态
   */
  async resetDailyStatus(): Promise<void> {
    try {
      const db = await getDb()

      await db
        .update(userAdInfo)
        .set({
          todayAdWatchCount: 0,
          updatedAt: getTime(),
        })

      console.log('每日状态重置完成')
    }
    catch (error) {
      console.error('重置每日状态失败', error)
    }
  }

  private async resetDailyStatusByUserId(userId: string, lastAdWatchAt?: Date | null): Promise<void> {
    try {
      const db = await getDb()
      if (lastAdWatchAt && isPreviousDate(lastAdWatchAt)) {
        await db
          .update(userAdInfo)
          .where(and(eq(userAdInfo.userId, userId), eq(userAdInfo.lastAdWatchAt, lastAdWatchAt)))
          .set({
            todayAdWatchCount: 0,
            updatedAt: getTime(),
          })
      }

      console.log('每日状态重置完成')
    }
    catch (error) {
      console.error('重置每日状态失败', error)
    }
  }
}

// 导出单例实例
export const userService = new UserService()
