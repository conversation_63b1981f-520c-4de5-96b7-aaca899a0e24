import { createRouter } from '@/lib/create-app'
import { describeRoute } from 'hono-openapi'
import { resolver, validator as zValidator } from 'hono-openapi/zod'
import z from 'zod'
import { jwtAuth } from '@/lib/jwt'
import { MailService } from '@/services/mail/mail-service'
import { globalResponse } from '@/types'
import {
  batchMailOperationSchema,
  claimMailRewardSchema,
  createMailSchema,
  getMailsSchema,
  mailListResponseSchema,
  mailResponseSchema,
  markMailAsReadSchema,
  unreadCountResponseSchema,
} from '@/types/mail'
import { getDaysDifferenceHour, getTime } from '@/utils/time'

const router = createRouter()

const mailService = new MailService()

// 获取用户邮件列表
router.post('/list', jwtAuth(), globalResponse({
  description: '获取用户邮件列表',
  tags: ['邮件'],
  responseSchema: mailListResponseSchema,
}), zValidator('json', getMailsSchema), async (c) => {
  try {
    const user = c.get('user')
    const userId = user.id
    const { isRead, includeExpired } = await c.req.json()

    const mails = await mailService.getUserMails(userId, {
      isRead,
      includeExpired,
    })

    const unreadCount = await mailService.getUnreadCount(userId)
    mails.forEach((mail) => {
      mail.agoHour = getDaysDifferenceHour(getTime(), mail.createdAt)
    })
    return c.json({
      success: true,
      data: {
        mails,
        total: mails.length,
        unreadCount,
      },
    })
  }
  catch (error) {
    console.error('获取邮件列表失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 获取用户邮件列表
router.post('/list/item', jwtAuth(), globalResponse({
  description: '获取用户邮件列表',
  tags: ['邮件'],
  responseSchema: mailResponseSchema,
}), zValidator('json', claimMailRewardSchema), async (c) => {
  try {
    // const user = c.get('user')
    // const userId = user.id
    const { mailId } = await c.req.json()

    const mail = await mailService.readMailById(mailId)

    mail.agoHour = getDaysDifferenceHour(getTime(), mail.createdAt)

    return c.json({
      success: true,
      data: mail,
    })
  }
  catch (error) {
    console.error('获取邮件列表失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 获取未读邮件数量
router.post('/unread-count', globalResponse({
  description: '获取未读邮件数量',
  tags: ['邮件'],
  responseSchema: unreadCountResponseSchema,
}), async (c) => {
  try {
    const user = c.get('user')
    const userId = user.id

    const count = await mailService.getUnreadCount(userId)

    return c.json({
      success: true,
      data: { count },
    })
  }
  catch (error) {
    console.error('获取未读邮件数量失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 标记邮件为已读
router.post('/mark-read', jwtAuth(), globalResponse({
  description: '标记邮件为已读',
  tags: ['邮件'],
  responseSchema: mailResponseSchema,
}), zValidator('json', markMailAsReadSchema), async (c) => {
  try {
    const { mailId } = await c.req.json()

    const result = await mailService.markMailAsRead(mailId)

    if (result.length === 0) {
      return c.json({ success: false, message: '邮件不存在或已读' }, 404)
    }

    return c.json({
      success: true,
      data: result[0],
    })
  }
  catch (error) {
    console.error('标记邮件为已读失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 领取邮件奖励
router.post('/claim-reward', jwtAuth(), globalResponse({
  description: '领取邮件奖励',
  tags: ['邮件'],
  responseSchema: mailResponseSchema,
}), zValidator('json', claimMailRewardSchema), async (c) => {
  try {
    const { mailId } = await c.req.json()

    const result = await mailService.claimMailReward(mailId)

    if (result.length === 0) {
      return c.json({ success: false, message: '邮件不存在或已领取' }, 404)
    }

    return c.json({
      success: true,
      data: result[0],
    })
  }
  catch (error) {
    console.error('领取邮件奖励失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 批量标记为已读
router.post('/batch-mark-read', jwtAuth(), globalResponse({
  description: '批量标记邮件为已读',
  tags: ['邮件'],
  responseSchema: z.array(mailResponseSchema),
}), zValidator('json', batchMailOperationSchema), async (c) => {
  try {
    const { mailIds } = await c.req.json()

    const result = await mailService.batchMarkAsRead(mailIds)

    return c.json({
      success: true,
      data: result,
    })
  }
  catch (error) {
    console.error('批量标记邮件为已读失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 批量领取奖励
router.post('/batch-claim-rewards', jwtAuth(), globalResponse({
  description: '批量领取邮件奖励',
  tags: ['邮件'],
  responseSchema: z.array(mailResponseSchema),
}), zValidator('json', batchMailOperationSchema), async (c) => {
  try {
    const { mailIds } = await c.req.json()

    const result = await mailService.batchClaimRewards(mailIds)

    return c.json({
      success: true,
      data: result,
    })
  }
  catch (error) {
    console.error('批量领取邮件奖励失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 创建邮件（管理员功能）
router.post('/create', globalResponse({
  description: '创建新邮件（管理员功能）',
  tags: ['邮件'],
  responseSchema: mailResponseSchema,
}), zValidator('json', createMailSchema), async (c) => {
  try {
    const { userId, title, rewards, expiredTime, content } = await c.req.json()
    const result = await mailService.createUserMail({
      userId,
      title,
      rewards,
      expiredTime: getTime(expiredTime),
      content,
    })

    return c.json({
      success: true,
      data: result[0],
    })
  }
  catch (error) {
    console.error('创建邮件失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 清理过期邮件
router.post('/cleanup-expired', globalResponse({
  description: '清理过期邮件',
  tags: ['邮件'],
}), async (c) => {
  try {
    const result = await mailService.cleanupExpiredMails()

    return c.json({
      success: true,
      data: {
        deletedCount: result.length,
        deletedMails: result,
      },
    })
  }
  catch (error) {
    console.error('清理过期邮件失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

export default router
