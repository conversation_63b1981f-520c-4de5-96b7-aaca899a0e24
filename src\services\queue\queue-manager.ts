import logger from '@/lib/logger'
import { archiveQueue } from './archive-queue'

/**
 * 队列管理器 - 统一管理所有队列的处理
 */
export class QueueManager {
  private processorIntervals: Map<string, NodeJS.Timeout> = new Map()
  private isShuttingDown = false

  /**
   * 启动所有队列处理器
   */
  async start(): Promise<void> {
    try {
      logger.info('启动队列管理器')

      // 启动存档队列处理器
      await this.startArchiveQueueProcessor()

      // 注册优雅关闭处理
      this.registerShutdownHandlers()

      logger.info('队列管理器启动完成')
    }
    catch (error) {
      logger.error('启动队列管理器失败', { message: error.message })
      throw error
    }
  }

  /**
   * 启动存档队列处理器
   */
  private async startArchiveQueueProcessor(): Promise<void> {
    try {
      // 导入存档服务（避免循环依赖）
      const { gameArchiveService } = await import('@/services/archive/game-archive-service')

      // 定义任务执行器
      const taskExecutor = async (task: any) => {
        if (task.type === 'update') {
          await gameArchiveService.updateArchiveInDatabase(
            task.userId,
            task.gameId,
            task.archiveId,
            task.data,
          )
        }
      }

      // 启动处理器
      const intervalId = archiveQueue.startProcessor(taskExecutor, 1000) // 每秒处理一次
      this.processorIntervals.set('archive', intervalId)

      logger.info('存档队列处理器已启动')
    }
    catch (error) {
      logger.error('启动存档队列处理器失败', { message: error.message })
      throw error
    }
  }

  /**
   * 停止所有队列处理器
   */
  async stop(): Promise<void> {
    if (this.isShuttingDown) {
      return
    }

    this.isShuttingDown = true
    logger.info('停止队列管理器')

    try {
      // 停止所有处理器
      for (const [name, intervalId] of this.processorIntervals) {
        archiveQueue.stopProcessor(intervalId)
        logger.info(`已停止 ${name} 队列处理器`)
      }

      this.processorIntervals.clear()
      logger.info('队列管理器已停止')
    }
    catch (error) {
      logger.error('停止队列管理器失败', { message: error.message })
    }
  }

  /**
   * 获取所有队列的状态
   */
  async getStatus(): Promise<{
    archive: any
    healthy: boolean
    issues: string[]
  }> {
    try {
      const archiveStats = await archiveQueue.getQueueStats()
      const archiveHealth = await archiveQueue.healthCheck()

      const allIssues = [...archiveHealth.issues]
      const isHealthy = archiveHealth.healthy

      return {
        archive: {
          stats: archiveStats,
          health: archiveHealth,
        },
        healthy: isHealthy,
        issues: allIssues,
      }
    }
    catch (error) {
      logger.error('获取队列状态失败', { message: error.message })
      return {
        archive: { stats: null, health: null },
        healthy: false,
        issues: [`获取状态失败: ${error.message}`],
      }
    }
  }

  /**
   * 清空所有队列
   */
  async clearAllQueues(): Promise<void> {
    try {
      await archiveQueue.clearQueue()
      logger.info('已清空所有队列')
    }
    catch (error) {
      logger.error('清空队列失败', { message: error.message })
      throw error
    }
  }

  /**
   * 手动触发队列处理
   */
  async processQueues(): Promise<void> {
    try {
      const { gameArchiveService } = await import('@/services/archive/game-archive-service')

      const taskExecutor = async (task: any) => {
        if (task.type === 'update') {
          await gameArchiveService.updateArchiveInDatabase(
            task.userId,
            task.gameId,
            task.archiveId,
            task.data,
          )
        }
      }

      await archiveQueue.processQueue(taskExecutor)
      logger.info('手动队列处理完成')
    }
    catch (error) {
      logger.error('手动队列处理失败', { message: error.message })
      throw error
    }
  }

  /**
   * 注册优雅关闭处理
   */
  private registerShutdownHandlers(): void {
    const shutdown = async (signal: string) => {
      logger.info(`收到 ${signal} 信号，开始优雅关闭队列管理器`)
      await this.stop()
      process.exit(0)
    }

    process.on('SIGTERM', () => shutdown('SIGTERM'))
    process.on('SIGINT', () => shutdown('SIGINT'))
    process.on('SIGUSR2', () => shutdown('SIGUSR2')) // nodemon 重启信号
  }

  /**
   * 获取队列管理器是否正在运行
   */
  isRunning(): boolean {
    return this.processorIntervals.size > 0 && !this.isShuttingDown
  }

  /**
   * 重启队列处理器
   */
  async restart(): Promise<void> {
    logger.info('重启队列管理器')
    await this.stop()
    await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
    await this.start()
  }
}

// 导出单例实例
export const queueManager = new QueueManager()

// 自动启动队列管理器
if (process.env.NODE_ENV !== 'test') {
  queueManager.start().catch((error) => {
    logger.error('自动启动队列管理器失败', { message: error.message })
    process.exit(1)
  })
}
