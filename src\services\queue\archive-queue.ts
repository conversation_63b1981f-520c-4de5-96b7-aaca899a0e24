import logger from '@/lib/logger'
import { getRedis } from '@/redis'

// 队列任务接口
export interface QueueTask {
  id: string
  type: 'update' | 'delete'
  userId: string
  gameId: string
  archiveId?: string // 可选，新的设计中不需要
  data?: any
  priority: 'high' | 'normal' | 'low'
  timestamp: number
  retries: number
}

export class ArchiveQueue {
  private readonly QUEUE_KEY = 'archive_update_queue'
  private readonly MAX_RETRIES = 3
  private readonly QUEUE_TTL = 86400 // 24小时
  private readonly MAX_QUEUE_LENGTH = 1000

  // 队列处理状态
  private isProcessingQueue = false

  /**
   * 添加任务到队列
   */
  async addTask(task: QueueTask): Promise<void> {
    try {
      const redis = await getRedis()
      const taskData = JSON.stringify(task)

      // 根据优先级添加到不同的队列
      const queueKey = `${this.QUEUE_KEY}:${task.priority}`

      // 使用正确的 Redis v5 方法名
      await redis.lPush(queueKey, taskData)
      await redis.expire(queueKey, this.QUEUE_TTL) // 防止队列堆积
      const queueLength = await redis.lLen(queueKey) // 获取队列长度

      // 队列长度告警
      if (Number(queueLength) > this.MAX_QUEUE_LENGTH) {
        logger.warn('队列长度过长', {
          queueKey,
          length: queueLength,
          taskId: task.id,
        })
      }

      logger.debug('任务已添加到队列', {
        taskId: task.id,
        priority: task.priority,
        type: task.type,
        queueLength,
      })
    }
    catch (error) {
      logger.error('添加任务到队列失败', { message: error.message, task })
      throw error
    }
  }

  /**
   * 处理队列中的任务
   */
  async processQueue(taskExecutor: (task: QueueTask) => Promise<void>): Promise<void> {
    if (this.isProcessingQueue) {
      return
    }
    this.isProcessingQueue = true

    try {
      const redis = await getRedis()
      const priorities = ['high', 'normal', 'low']

      for (const priority of priorities) {
        const queueKey = `${this.QUEUE_KEY}:${priority}`

        // 处理该优先级的所有任务
        while (true) {
          const taskData = await redis.rPop(queueKey)
          if (!taskData)
            break

          try {
            const taskStr = typeof taskData === 'string' ? taskData : String(taskData)
            const task: QueueTask = JSON.parse(taskStr)
            await taskExecutor(task)
          }
          catch (error) {
            logger.error('处理队列任务失败', { message: error.message, taskData })
          }
        }
      }
    }
    catch (error) {
      logger.error('处理队列失败', { message: error.message })
    }
    finally {
      this.isProcessingQueue = false
    }
  }

  /**
   * 立即处理所有队列任务（付费相关等重要操作）
   */
  async flushQueue(
    taskExecutor: (task: QueueTask) => Promise<void>,
    userId?: string,
    gameId?: string,
  ): Promise<void> {
    try {
      const redis = await getRedis()
      const priorities = ['high', 'normal', 'low']
      let processedCount = 0

      for (const priority of priorities) {
        const queueKey = `${this.QUEUE_KEY}:${priority}`
        const tasks: QueueTask[] = []

        // 获取所有任务
        while (true) {
          const taskData = await redis.rPop(queueKey)
          if (!taskData)
            break

          try {
            const taskStr = typeof taskData === 'string' ? taskData : String(taskData)
            const task: QueueTask = JSON.parse(taskStr)

            // 如果指定了用户和游戏，只处理相关任务
            if (userId && gameId) {
              if (task.userId === userId && task.gameId === gameId) {
                tasks.push(task)
              }
              else {
                // 不相关的任务放回队列
                await redis.lPush(queueKey, taskData)
              }
            }
            else {
              tasks.push(task)
            }
          }
          catch (error) {
            logger.error('解析队列任务失败', { message: error.message, taskData })
          }
        }

        // 执行相关任务
        for (const task of tasks) {
          try {
            await taskExecutor(task)
            processedCount++
          }
          catch (error) {
            logger.error('执行队列任务失败', {
              message: error.message,
              taskId: task.id,
            })

            // 重试机制
            if (task.retries < this.MAX_RETRIES) {
              task.retries++
              task.timestamp = Date.now()
              await this.addTask(task)
            }
            else {
              logger.error('队列任务重试次数超限，放弃执行', { taskId: task.id })
            }
          }
        }
      }

      logger.info('队列刷新完成', { userId, gameId, processedCount })
    }
    catch (error) {
      logger.error('刷新队列失败', { message: error.message })
      throw error
    }
  }

  /**
   * 获取队列统计信息
   */
  async getQueueStats(): Promise<{
    high: number
    normal: number
    low: number
    total: number
  }> {
    try {
      const redis = await getRedis()

      // 使用并行查询而不是 pipeline
      const [high, normal, low] = await Promise.all([
        redis.lLen(`${this.QUEUE_KEY}:high`),
        redis.lLen(`${this.QUEUE_KEY}:normal`),
        redis.lLen(`${this.QUEUE_KEY}:low`),
      ])

      return {
        high: Number(high) || 0,
        normal: Number(normal) || 0,
        low: Number(low) || 0,
        total: Number(high) + Number(normal) + Number(low),
      }
    }
    catch (error) {
      logger.error('获取队列统计失败', { message: error.message })
      return { high: 0, normal: 0, low: 0, total: 0 }
    }
  }

  /**
   * 清空指定优先级的队列
   */
  async clearQueue(priority?: 'high' | 'normal' | 'low'): Promise<void> {
    try {
      const redis = await getRedis()

      if (priority) {
        const queueKey = `${this.QUEUE_KEY}:${priority}`
        await redis.del(queueKey)
        logger.info('已清空队列', { priority })
      }
      else {
        // 清空所有队列
        await Promise.all([
          redis.del(`${this.QUEUE_KEY}:high`),
          redis.del(`${this.QUEUE_KEY}:normal`),
          redis.del(`${this.QUEUE_KEY}:low`),
        ])
        logger.info('已清空所有队列')
      }
    }
    catch (error) {
      logger.error('清空队列失败', { message: error.message, priority })
      throw error
    }
  }

  /**
   * 检查队列健康状态
   */
  async healthCheck(): Promise<{
    healthy: boolean
    issues: string[]
    stats: any
  }> {
    const issues: string[] = []

    try {
      const stats = await this.getQueueStats()

      // 检查队列长度
      if (stats.total > this.MAX_QUEUE_LENGTH) {
        issues.push(`队列总长度过长: ${stats.total}`)
      }

      if (stats.high > 100) {
        issues.push(`高优先级队列过长: ${stats.high}`)
      }

      // 检查 Redis 连接
      const redis = await getRedis()
      await redis.ping()

      return {
        healthy: issues.length === 0,
        issues,
        stats,
      }
    }
    catch (error) {
      issues.push(`Redis 连接失败: ${error.message}`)
      return {
        healthy: false,
        issues,
        stats: { high: 0, normal: 0, low: 0, total: 0 },
      }
    }
  }

  /**
   * 启动队列处理器（定期处理）
   */
  startProcessor(
    taskExecutor: (task: QueueTask) => Promise<void>,
    intervalMs: number = 5000,
  ): NodeJS.Timeout {
    const intervalId = setInterval(async () => {
      try {
        await this.processQueue(taskExecutor)
      }
      catch (error) {
        logger.error('队列处理器执行失败', { message: error.message })
      }
    }, intervalMs)

    logger.info('队列处理器已启动', { intervalMs })
    return intervalId
  }

  /**
   * 停止队列处理器
   */
  stopProcessor(intervalId: NodeJS.Timeout): void {
    clearInterval(intervalId)
    logger.info('队列处理器已停止')
  }
}

// 导出单例实例
export const archiveQueue = new ArchiveQueue()
