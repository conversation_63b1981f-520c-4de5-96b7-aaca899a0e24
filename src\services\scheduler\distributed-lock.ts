import { schedulerLogger } from '@/lib/logger'
import { getRedis } from '@/redis'

/**
 * 分布式锁服务
 * 用于防止定时任务在多个实例间重复执行
 */
export class DistributedLock {
  private redis: any
  private lockPrefix: string
  private defaultTTL = 600
  private environment: string

  constructor() {
    this.redis = null
    this.environment = this.getEnvironment()
    this.lockPrefix = `${this.environment}_scheduler_lock:`

    schedulerLogger.info('分布式锁初始化', {
      environment: this.environment,
      lockPrefix: this.lockPrefix,
    })
  }

  async acquire(key: string, ttl: number): Promise<boolean> {
    const redis = await getRedis()
    const result = await redis.setEx(key, ttl, '1')
    return result === 'OK'
  }

  async release(key: string): Promise<void> {
    const redis = await getRedis()
    await redis.del(key)
  }

  /**
   * 获取当前环境标识
   */
  private getEnvironment(): string {
    // 优先使用自定义环境变量
    if (process.env.ENVIRONMENT) {
      return process.env.ENVIRONMENT.toLowerCase()
    }

    // 其次使用NODE_ENV
    if (process.env.NODE_ENV) {
      return process.env.NODE_ENV.toLowerCase()
    }

    // 默认为development
    return 'development'
  }

  /**
   * 获取Redis连接（带重试机制）
   */
  private async getRedisClient() {
    if (!this.redis) {
      try {
        this.redis = await getRedis()
        schedulerLogger.debug('Redis连接已建立', { environment: this.environment })
      }
      catch (error) {
        schedulerLogger.error('获取Redis连接失败', {
          message: error.message,
          environment: this.environment,
        })
        throw error
      }
    }

    // 检查连接是否仍然有效
    try {
      await this.redis.ping()
    }
    catch (error) {
      schedulerLogger.warn('Redis连接已断开，重新连接', {
        message: error.message,
        environment: this.environment,
      })

      // 重新获取连接
      this.redis = null
      this.redis = await getRedis()
    }

    return this.redis
  }

  /**
   * 尝试获取锁
   * @param lockKey 锁的唯一标识
   * @param ttlSeconds 锁的过期时间（秒）
   * @param instanceId 实例ID，用于标识锁的持有者
   * @returns 是否成功获取锁
   */
  async acquireLock(
    lockKey: string,
    ttlSeconds: number = this.defaultTTL,
    instanceId: string = this.generateInstanceId(),
  ): Promise<boolean> {
    try {
      const redis = await this.getRedisClient()
      const fullKey = `${this.lockPrefix}${lockKey}`

      // 使用SET命令的NX和EX选项实现原子性锁获取
      const result = await redis.set(fullKey, instanceId, 'EX', ttlSeconds, 'NX')

      const acquired = result === 'OK'

      if (acquired) {
        schedulerLogger.debug('成功获取分布式锁', {
          lockKey,
          instanceId,
          ttlSeconds,
        })
      }
      else {
        schedulerLogger.debug('获取分布式锁失败，锁已被其他实例持有', {
          lockKey,
          instanceId,
        })
      }

      return acquired
    }
    catch (error) {
      schedulerLogger.error('获取分布式锁时发生错误', {
        lockKey,
        message: error.message,
      })
      return false
    }
  }

  /**
   * 释放锁
   * @param lockKey 锁的唯一标识
   * @param instanceId 实例ID，只有锁的持有者才能释放
   * @returns 是否成功释放锁
   */
  async releaseLock(lockKey: string, instanceId: string): Promise<boolean> {
    try {
      const redis = await this.getRedisClient()
      const fullKey = `${this.lockPrefix}${lockKey}`

      // 先检查锁是否存在
      const currentValue = await redis.get(fullKey)

      if (!currentValue) {
        schedulerLogger.debug('锁已不存在，可能已过期', {
          lockKey,
          instanceId,
          fullKey,
        })
        return true // 锁不存在视为释放成功
      }

      if (currentValue !== instanceId) {
        schedulerLogger.warn('锁不是当前实例持有，无法释放', {
          lockKey,
          instanceId,
          currentValue,
          fullKey,
        })
        return false
      }

      // 使用Lua脚本确保原子性：只有锁的持有者才能释放锁
      const luaScript = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("DEL", KEYS[1])
        else
          return 0
        end
      `

      const result = await redis.eval(luaScript, 1, fullKey, instanceId)
      const released = result === 1

      if (released) {
        schedulerLogger.debug('成功释放分布式锁', {
          lockKey,
          instanceId,
          fullKey,
        })
      }
      else {
        schedulerLogger.warn('Lua脚本释放锁失败', {
          lockKey,
          instanceId,
          result,
          fullKey,
        })
      }

      return released
    }
    catch (error) {
      schedulerLogger.error('释放分布式锁时发生错误', {
        lockKey,
        instanceId,
        message: error.message,
        stack: error.stack,
        errorType: error.constructor.name,
      })

      // 即使释放失败，也不应该影响程序继续运行
      // 锁会自动过期，所以返回true避免阻塞
      schedulerLogger.warn('锁释放失败，但继续执行（锁会自动过期）', { lockKey, instanceId })
      return true
    }
  }

  /**
   * 续期锁
   * @param lockKey 锁的唯一标识
   * @param instanceId 实例ID
   * @param ttlSeconds 新的过期时间
   * @returns 是否成功续期
   */
  async renewLock(
    lockKey: string,
    instanceId: string,
    ttlSeconds: number = this.defaultTTL,
  ): Promise<boolean> {
    try {
      const redis = await this.getRedisClient()
      const fullKey = `${this.lockPrefix}${lockKey}`

      // 使用Lua脚本确保原子性：只有锁的持有者才能续期
      const luaScript = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("EXPIRE", KEYS[1], ARGV[2])
        else
          return 0
        end
      `

      const result = await redis.eval(luaScript, 1, fullKey, instanceId, ttlSeconds)
      const renewed = result === 1

      if (renewed) {
        schedulerLogger.debug('成功续期分布式锁', {
          lockKey,
          instanceId,
          ttlSeconds,
        })
      }

      return renewed
    }
    catch (error) {
      schedulerLogger.error('续期分布式锁时发生错误', {
        lockKey,
        instanceId,
        message: error.message,
      })
      return false
    }
  }

  /**
   * 检查锁是否存在
   * @param lockKey 锁的唯一标识
   * @returns 锁是否存在
   */
  async isLocked(lockKey: string): Promise<boolean> {
    try {
      const redis = await this.getRedisClient()
      const fullKey = `${this.lockPrefix}${lockKey}`

      const exists = await redis.exists(fullKey)
      return exists === 1
    }
    catch (error) {
      schedulerLogger.error('检查分布式锁状态时发生错误', {
        lockKey,
        message: error.message,
      })
      return false
    }
  }

  /**
   * 生成实例ID
   * 结合环境、主机名、进程ID和随机数生成唯一标识
   */
  private generateInstanceId(): string {
    const hostname = process.env.HOSTNAME || process.env.INSTANCE_ID || 'unknown'
    const pid = process.pid
    const random = Math.random().toString(36).substring(2, 8)
    const timestamp = Date.now()

    return `${this.environment}-${hostname}-${pid}-${random}-${timestamp}`
  }

  /**
   * 执行带锁的任务
   * @param lockKey 锁的唯一标识
   * @param task 要执行的任务函数
   * @param ttlSeconds 锁的过期时间
   * @returns 任务执行结果
   */
  async executeWithLock<T>(
    lockKey: string,
    task: () => Promise<T>,
    ttlSeconds: number = this.defaultTTL,
  ): Promise<T | null> {
    const instanceId = this.generateInstanceId()

    // 尝试获取锁
    const acquired = await this.acquireLock(lockKey, ttlSeconds, instanceId)

    if (!acquired) {
      schedulerLogger.info('任务跳过执行，锁已被其他实例持有', { lockKey })
      return null
    }

    try {
      schedulerLogger.info('开始执行带锁任务', { lockKey, instanceId })

      // 执行任务
      const result = await task()

      schedulerLogger.info('带锁任务执行完成', { lockKey, instanceId })
      return result
    }
    catch (error) {
      schedulerLogger.error('带锁任务执行失败', {
        lockKey,
        instanceId,
        message: error.message,
      })
      throw error
    }
    finally {
      // 释放锁
      await this.releaseLock(lockKey, instanceId)
    }
  }
}

// 导出单例实例
export const distributedLock = new DistributedLock()
