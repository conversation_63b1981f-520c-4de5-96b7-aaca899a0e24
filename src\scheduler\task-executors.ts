import { shouldExecuteTask } from '@/common'
import { getMessageTasks } from '@/config'
import { schedulerLogger } from '@/lib/logger'
import { partitionManager } from '@/services/scheduler/partition-manager'
// import { PrivilegeCardScheduler } from './scheduler-user'

/**
 * 任务执行器接口
 */
export interface TaskExecutor {
  name: string
  execute: () => Promise<void>
}

/**
 * 特权卡相关任务执行器
 */
export class PrivilegeCardTaskExecutors {
  // private privilegeScheduler: PrivilegeCardScheduler

  constructor() {
    // this.privilegeScheduler = new PrivilegeCardScheduler()
  }

  /**
   * 发送未领取奖励任务执行器
   */
  sendUnclaimedRewards: TaskExecutor = {
    name: 'sendUnclaimedRewards',
    execute: async () => {
      // await this.privilegeScheduler.sendUnclaimedRewardsToMail()
    },
  }

  /**
   * 重置每日状态任务执行器
   */
  resetDailyStatus: TaskExecutor = {
    name: 'resetDailyStatus',
    execute: async () => {
      // await this.privilegeScheduler.resetDailyStatus()
    },
  }

  /**
   * 清理过期数据任务执行器
   */
  cleanupExpiredData: TaskExecutor = {
    name: 'cleanupExpiredData',
    execute: async () => {
      // await this.privilegeScheduler.cleanupExpiredData()
    },
  }
}

/**
 * 分区管理任务执行器
 */
export class PartitionTaskExecutors {
  /**
   * 创建分区任务执行器
   */
  createPartitions: TaskExecutor = {
    name: 'createPartitions',
    execute: async () => {
      await partitionManager.createNextMonthPartitions()
    },
  }

  /**
   * 清理过期分区任务执行器
   */
  cleanupPartitions: TaskExecutor = {
    name: 'cleanupPartitions',
    execute: async () => {
      // 这里可以从配置中读取保留月数，暂时使用默认值
      await partitionManager.cleanupOldPartitions()
    },
  }

  /**
   * 分区统计任务执行器
   */
  partitionStats: TaskExecutor = {
    name: 'partitionStats',
    execute: async () => {
      const stats = await partitionManager.getPartitionStatsByConfig()
      schedulerLogger.info('分区统计信息', {
        tablesCount: Object.keys(stats.tables).length,
        totalPartitions: stats.summary.totalPartitions,
        totalRows: stats.summary.totalRows,
      })
    },
  }
}

/**
 * 消息任务执行器
 */
export class MessageTaskExecutors {
  /**
   * 检查并执行消息任务执行器
   */
  checkMessageTasks: TaskExecutor = {
    name: 'checkMessageTasks',
    execute: async () => {
      try {
        // 从数据库加载最新的任务配置
        const messageTasks = await getMessageTasks()

        if (messageTasks.length === 0) {
          schedulerLogger.debug('没有找到启用的定时任务')
          return
        }

        for (const task of messageTasks) {
          if (shouldExecuteTask(task.cronExpression)) {
            schedulerLogger.info('执行消息任务', { tplId: task.tplId })
            // 这里可以添加具体的消息任务执行逻辑
            // await executeMessageTask(task)
          }
        }
      }
      catch (error) {
        schedulerLogger.error('消息任务执行失败', { error: error.message })
        throw error
      }
    },
  }
}

/**
 * 创建所有任务执行器实例
 */
export function createTaskExecutors() {
  const privilegeExecutors = new PrivilegeCardTaskExecutors()
  const partitionExecutors = new PartitionTaskExecutors()
  const messageExecutors = new MessageTaskExecutors()

  return {
    // 特权卡任务
    sendUnclaimedRewards: privilegeExecutors.sendUnclaimedRewards,
    resetDailyStatus: privilegeExecutors.resetDailyStatus,
    cleanupExpiredData: privilegeExecutors.cleanupExpiredData,

    // 分区管理任务
    createPartitions: partitionExecutors.createPartitions,
    cleanupPartitions: partitionExecutors.cleanupPartitions,
    partitionStats: partitionExecutors.partitionStats,

    // 消息任务
    checkMessageTasks: messageExecutors.checkMessageTasks,
  }
}
