import type { <PERSON><PERSON>, Schema } from 'hono'
import type { <PERSON><PERSON><PERSON>og<PERSON> } from 'hono-pino'
import type { JWTPayload } from './jwt'

export interface AppBindings {
  Variables: {
    logger: PinoLogger
    // Added variables used across middlewares/routes
    requestId: string
    user: JWTPayload
  }
};

// eslint-disable-next-line ts/no-empty-object-type
export type AppOpenAPI<S extends Schema = {}> = Hono<AppBindings, S>
