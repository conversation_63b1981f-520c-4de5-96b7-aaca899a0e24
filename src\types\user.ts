import z from 'zod'
// import 'zod-openapi/extend'

// 基础字段 schema
export const idSchema = z.string().min(1, '用户ID不能为空').meta({
  example: '45b4b468-caf8-4581-b682-195a13558c19',
  description: '用户唯一标识符(字段名为userId的都是传这个)',
})
export const gameIdSchema = z.string().meta({
  example: '1:************:android:40c492488c1236409af566r',
  description: '游戏ID',
})

export const uidSchema = z.string().min(1, '用户ID不能为空').meta({
  example: '27694583',
  description: '用户唯一标识符(昊天)',
})

export const userNameSchema = z.string().min(1, '用户名不能为空').meta({
  example: '<EMAIL>',
  description: '用户显示名称',
})

export const tokenSchema = z.string().optional().meta({
  example: '@156@151@190@118@196@167@190@169@99@124@12160',
  description: '用户认证令牌',
})

export const isGuestSchema = z.boolean().default(false).meta({
  example: false,
  description: '是否为游客用户',
})

export const isNewUserSchema = z.boolean().default(true).meta({
  example: false,
  description: '是否为新用户',
})

export const isPreRegSchema = z.boolean().default(false).meta({
  example: false,
  description: '是否为预注册用户',
})

export const openTypeSchema = z.string().optional().meta({
  example: '13',
  description: '第三方登录类型',
})

export const avatarSchema = z.string().optional().meta({
  example: 'https://example.com/avatar.jpg',
  description: '用户头像URL',
})

export const nicknameSchema = z.string().optional().meta({
  example: '<EMAIL>',
  description: '用户昵称',
})

export const dataSchema = z.record(z.any(), z.any()).meta({
  example: { customField: 'customValue' },
  description: '用户自定义数据',
})

export const lastLoginAtSchema = z.date().optional().meta({
  example: '2024-01-01T00:00:00.000Z',
  description: '最后登录时间',
})

export const loginCountSchema = z.number().int().min(0).default(0).meta({
  example: 15,
  description: '登录次数',
})

export const createdAtSchema = z.date().meta({
  example: '2024-01-01T00:00:00.000Z',
  description: '创建时间',
})

export const updatedAtSchema = z.date().meta({
  example: '2024-01-01T00:00:00.000Z',
  description: '更新时间',
})

export const userProfileSchema = z.object({
  id: idSchema,
  uid: uidSchema,
  userName: userNameSchema.optional(),
  token: tokenSchema,
  isGuest: isGuestSchema,
  isNewUser: isNewUserSchema,
  isPreReg: isPreRegSchema,
  openType: openTypeSchema,
  avatar: avatarSchema.optional(),
  nickname: nicknameSchema.optional(),
  data: dataSchema.optional(),
  lastLoginAt: lastLoginAtSchema.optional(),
  loginCount: loginCountSchema.optional(),
  createdAt: createdAtSchema.optional(),
  updatedAt: updatedAtSchema.optional(),
})

export const createUserRequestSchema = userProfileSchema.omit({ id: true, createdAt: true, updatedAt: true, lastLoginAt: true, loginCount: true, data: true })

export const updateUserRequestSchema = createUserRequestSchema.pick({
  token: true,
  avatar: true,
  nickname: true,
})

// 导出 TypeScript 类型
export type UserProfile = z.infer<typeof userProfileSchema>
export type CreateUserRequest = z.infer<typeof createUserRequestSchema>
export type UpdateUserRequest = z.infer<typeof updateUserRequestSchema>

// 导出基础字段类型，方便复用
export type Uid = z.infer<typeof uidSchema>
export type UserName = z.infer<typeof userNameSchema>
export type Token = z.infer<typeof tokenSchema>
export type IsGuest = z.infer<typeof isGuestSchema>
export type IsNewUser = z.infer<typeof isNewUserSchema>
export type IsPreReg = z.infer<typeof isPreRegSchema>
export type OpenType = z.infer<typeof openTypeSchema>
export type Avatar = z.infer<typeof avatarSchema>
export type Nickname = z.infer<typeof nicknameSchema>
export type UserData = z.infer<typeof dataSchema>
export type LastLoginAt = z.infer<typeof lastLoginAtSchema>
export type LoginCount = z.infer<typeof loginCountSchema>
export type CreatedAt = z.infer<typeof createdAtSchema>
export type UpdatedAt = z.infer<typeof updatedAtSchema>

// 响应 schema
export const userProfileResponseSchema = z.object({
  success: z.boolean().meta({ example: true }),
  data: userProfileSchema,
})

export const createUserResponseSchema = z.object({
  success: z.boolean().meta({ example: true }),
  data: userProfileSchema,
})

export const updateUserResponseSchema = z.object({
  success: z.boolean().meta({ example: true }),
  data: userProfileSchema,
})

// 响应类型
export type UserProfileResponse = z.infer<typeof userProfileResponseSchema>
export type CreateUserResponse = z.infer<typeof createUserResponseSchema>
export type UpdateUserResponse = z.infer<typeof updateUserResponseSchema>
