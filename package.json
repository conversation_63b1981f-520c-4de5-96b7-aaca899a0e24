{"name": "nqhy-inter", "version": "1.0.0", "main": "index.js", "packageManager": "pnpm@8.15.0", "type": "module", "scripts": {"dev": "chcp 65001 && tsx watch src/index.ts", "start": "node dist/index.js", "start:ts": "node -r tsconfig-paths/register -r tsx/cjs src/index.ts", "build": "npx tsc --noEmitOnError false", "lint": "eslint .", "ssh:tunnel": "node src/ssh-tunnel.js", "db:studio": "drizzle-kit studio", "lint:fix": "eslint \"src/**/*.{js,ts}\" --fix", "generate": "drizzle-kit generate", "migrate": "drizzle-kit migrate", "docker:build": "docker build -t nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-inter-site:latest .", "docker:build:no-cache": "docker build -t nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-inter-site:latest .  --no-cache", "docker:push": "docker push nanquanhuyusite-cn-beijing.cr.volces.com/nqhy/nqhy-inter-site:latest", "docker:save": "docker save nqhy-inter:latest -o nqhy-inter.tar"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["typescript"]}, "overrides": {"eslint": "^8.57.0"}}, "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "keywords": ["hono", "drizzle", "mysql", "backend", "typescript", "ssh-tunnel"], "author": "", "license": "ISC", "description": "", "dependencies": {"@hono/node-server": "^1.17.1", "@hono/node-ws": "^1.2.0", "@hono/zod-validator": "^0.7.2", "@scalar/hono-api-reference": "^0.9.12", "dayjs": "^1.11.13", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.8.2", "hono": "^4.8.5", "hono-openapi": "^0.4.8", "hono-pino": "^0.10.1", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "mysql2": "^3.14.2", "node-schedule": "^2.1.1", "pg": "^8.16.3", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "redis": "^5.6.1", "ssh2": "^1.16.0", "stoker": "^1.4.3", "zod": "^4.1.5", "zod-openapi": "5.4.0"}, "devDependencies": {"@antfu/eslint-config": "^4.18.0", "@eslint/css": "^0.10.0", "@eslint/js": "^9.31.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "@types/node": "^24.1.0", "@types/node-schedule": "^2.1.8", "@types/pg": "^8.15.5", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "drizzle-kit": "^0.31.4", "eslint": "8.57.1", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}