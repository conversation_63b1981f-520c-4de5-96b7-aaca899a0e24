import * as schedule from 'node-schedule'
import { schedulerLogger } from '@/lib/logger'
import { getRedis } from '@/redis'
import { distributedLock } from '@/services/scheduler/distributed-lock'
import { formatCurTime, getTime } from '@/utils/time'

/**
 * 任务执行器接口
 */

/**
 * 调度任务配置
 */
interface ScheduledTaskConfig {
  name: string
  cronExpression: string
  executor: () => Promise<void>
  lockTTL?: number // 锁的过期时间（秒）
  enabled?: boolean
}

/**
 * 统一的调度器管理器
 * 负责管理所有定时任务的执行、锁机制和错误处理
 */
export class SchedulerManager {
  private jobs: schedule.Job[] = []
  private tasks: Map<string, ScheduledTaskConfig> = new Map()
  private instanceId: string
  private isRunning = false

  constructor() {
    this.instanceId = process.env.INSTANCE_ID || `scheduler-${Date.now()}`
    schedulerLogger.info('调度器管理器初始化', { instanceId: this.instanceId })
  }

  /**
   * 注册任务
   */
  registerTask(config: ScheduledTaskConfig): void {
    this.tasks.set(config.name, {
      lockTTL: 3600, // 默认1小时锁
      enabled: true,
      ...config,
    })

    schedulerLogger.info('任务已注册', {
      taskName: config.name,
      cronExpression: config.cronExpression,
    })
  }

  /**
   * 启动所有调度任务
   */
  start(): void {
    if (this.isRunning) {
      schedulerLogger.warn('调度器已在运行')
      return
    }

    this.isRunning = true
    schedulerLogger.info('启动调度器管理器', {
      instanceId: this.instanceId,
      tasksCount: this.tasks.size,
    })

    // 为每个任务创建调度
    for (const [taskName, config] of this.tasks) {
      if (!config.enabled) {
        schedulerLogger.info('任务已禁用，跳过调度', { taskName })
        continue
      }

      const job = schedule.scheduleJob(config.cronExpression, async () => {
        await this.executeTaskWithLock(taskName, config)
      })

      if (job) {
        this.jobs.push(job)
        schedulerLogger.info('任务调度已创建', {
          taskName,
          cronExpression: config.cronExpression,
        })
      }
      else {
        schedulerLogger.error('任务调度创建失败', {
          taskName,
          cronExpression: config.cronExpression,
        })
      }
    }

    // 创建补偿任务检查器 - 每15分钟检查一次遗漏的任务
    const catchupJob = schedule.scheduleJob('*/15 * * * *', async () => {
      await this.checkAndExecuteMissedTasks()
    })

    if (catchupJob) {
      this.jobs.push(catchupJob)
      schedulerLogger.info('补偿任务检查器已启动')
    }

    schedulerLogger.info('调度器管理器启动完成', {
      totalJobs: this.jobs.length,
    })
  }

  /**
   * 停止所有调度任务
   */
  stop(): void {
    this.isRunning = false

    this.jobs.forEach((job) => {
      if (job) {
        job.cancel()
      }
    })
    this.jobs = []

    schedulerLogger.info('调度器管理器已停止')
  }

  /**
   * 使用分布式锁执行任务
   */
  private async executeTaskWithLock(taskName: string, config: ScheduledTaskConfig): Promise<void> {
    const lockKey = `scheduler_task:${taskName}`
    const today = getTime().toISOString().split('T')[0]
    const taskInstanceId = `${this.instanceId}_${taskName}`

    try {
      // 检查 Redis 中是否已执行过
      const redis = await getRedis()
      const redisKey = `task_executed:${taskName}:${today}`
      const hasExecuted = await redis.get(redisKey)

      if (hasExecuted) {
        schedulerLogger.debug('任务今日已执行过，跳过', { taskName })
        return
      }

      // 尝试获取分布式锁
      const lockAcquired = await distributedLock.acquireLock(
        lockKey,
        config.lockTTL!,
        taskInstanceId,
      )

      if (!lockAcquired) {
        schedulerLogger.info('任务已被其他实例执行，跳过', { taskName })
        return
      }

      schedulerLogger.info('开始执行任务', { taskName })

      // 执行任务
      await config.executor()

      // 标记任务已执行
      const tomorrow = getTime()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)
      const ttl = Math.floor((tomorrow.getTime() - Date.now()) / 1000)
      await redis.setEx(redisKey, ttl, '1')

      schedulerLogger.info('任务执行成功', { taskName })
    }
    catch (error) {
      schedulerLogger.error('任务执行失败', {
        taskName,
        error: error.message,
      })
    }
    finally {
      // 释放分布式锁
      try {
        await distributedLock.releaseLock(lockKey, taskInstanceId)
      }
      catch (lockError) {
        schedulerLogger.warn('释放分布式锁失败', {
          taskName,
          lockKey,
          error: lockError.message,
        })
      }
    }
  }

  /**
   * 检查并执行遗漏的任务
   */
  private async checkAndExecuteMissedTasks(): Promise<void> {
    try {
      const redis = await getRedis()
      const now = getTime()
      const today = now.toISOString().split('T')[0]

      schedulerLogger.debug('检查遗漏任务', { timestamp: now.toISOString() })

      for (const [taskName, config] of this.tasks) {
        if (!config.enabled)
          continue

        // 检查任务是否应该在今天执行过但没有执行
        const shouldHaveExecuted = this.shouldTaskHaveExecutedToday(config.cronExpression, now)

        if (shouldHaveExecuted) {
          const redisKey = `task_executed:${taskName}:${today}`
          const hasExecuted = await redis.get(redisKey)

          if (!hasExecuted) {
            schedulerLogger.info('发现遗漏任务，开始补执行', { taskName })
            await this.executeTaskWithLock(taskName, config)
          }
        }
      }
    }
    catch (error) {
      schedulerLogger.error('检查遗漏任务失败', { error: error.message })
    }
  }

  /**
   * 判断任务是否应该在今天执行过
   */
  private shouldTaskHaveExecutedToday(cronExpression: string, now: Date): boolean {
    // 简单的判断逻辑，可以根据需要扩展
    const hour = now.getHours()
    const dayOfWeek = now.getDay()

    // 每小时任务：如果当前时间超过1点，说明应该执行过
    if (cronExpression === '0 * * * *' && hour >= 1) {
      return true
    }

    // 每日任务：如果当前时间超过1点，说明应该执行过
    if (cronExpression === '0 0 * * *' && hour >= 1) {
      return true
    }

    // 每周任务：如果是周日且时间超过1点，说明应该执行过
    if (cronExpression === '0 0 * * 0' && dayOfWeek === 0 && hour >= 1) {
      return true
    }

    return false
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(): any {
    const status = {
      isRunning: this.isRunning,
      instanceId: this.instanceId,
      totalTasks: this.tasks.size,
      enabledTasks: Array.from(this.tasks.values()).filter(t => t.enabled).length,
      totalJobs: this.jobs.length,
      tasks: Array.from(this.tasks.entries()).map(([name, config]) => ({
        name,
        cronExpression: config.cronExpression,
        enabled: config.enabled,
        lockTTL: config.lockTTL,
      })),
    }

    return status
  }
}

// 导出单例实例
export const schedulerManager = new SchedulerManager()
