import { and, eq, gte } from 'drizzle-orm'
import { REWARD_CONFIG } from '@/config'
import { getDb } from '@/db'
import { privilegeRecords } from '@/schema'
import { formatCurTime, getTime, isPreviousDate } from '@/utils/time'
import { MailService } from '../mail/mail-service'

const mailService = new MailService()
export class PrivilegeCardFunc {
  static async sendUnclaimedRewardsToMail(userId: string): Promise<void> {
    try {
      const db = await getDb()
      const yesterday = getTime()
      yesterday.setDate(yesterday.getDate() - 1)
      yesterday.setHours(23, 59, 59, 999)

      // 查找有激活特权卡的用户（从privilegeRecords表查询）
      const usersWithActiveCards = await db
        .select({
          userId: privilegeRecords.userId,
          rewardType: privilegeRecords.rewardType,
          endDate: privilegeRecords.endDate,
          claimDates: privilegeRecords.claimDates,
        })
        .from(privilegeRecords)
        .where(and(
          eq(privilegeRecords.userId, userId),
          eq(privilegeRecords.status, 'active'),
          gte(privilegeRecords.endDate, yesterday),
        ))

      const userRecords = new Map<string, any[]>()
      for (const record of usersWithActiveCards) {
        if (!userRecords.has(record.userId)) {
          userRecords.set(record.userId, [])
        }
        userRecords.get(record.userId)!.push(record)
      }

      for (const record of usersWithActiveCards) {
        let hasClaimedYesterday = false

        const claimDates = (record.claimDates as string[]) || []
        // 使用 isToday 函数来比较昨日日期，支持 'YYYY-MM-DD HH:mm:ss' 格式
        if (claimDates.some((date) => {
          return isPreviousDate(date)
        })) {
          hasClaimedYesterday = true
          break
        }
        const reward = REWARD_CONFIG[record.rewardType]
        if (!hasClaimedYesterday) {
          mailService.createUserMail({
            userId,
            title: '未领取特权卡奖励提醒',
            rewards: reward,
            expiredTime: getTime(),
            content: '您有未领取的奖励，请及时查看',
          })
          console.log(`发送奖励到邮箱 - 用户: ${userId}, 奖励:`, reward)
          console.log(`发送未领取奖励到邮箱: ${userId}`)
        }
      }

      console.log('发送未领取奖励到邮箱完成')
    }
    catch (error) {
      console.error('发送未领取奖励到邮箱失败', error)
    }
  }
}
