/**
 * 时间工具函数
 */
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
export const TIMEZONE = 'Asia/Shanghai'
// 启用插件
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * 格式化时间为时区字符串 
 * @param date Date 对象
 * @returns 格式化的时区时间字符串 
 */
export function formatCurTime(date: Date): string {
  const dayjsDate = dayjs(date)
  if (!dayjsDate.isValid()) {
    throw new Error('Invalid date provided to formatCurTime')
  }
  return dayjsDate.tz(TIMEZONE).add(0).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化时间为时区 ISO 格式字符串
 * @param date Date 对象
 * @returns 格式化的时区 ISO 时间字符串
 */
export function formatCurTimeISO(date: Date): string {
  const dayjsDate = dayjs(date)
  if (!dayjsDate.isValid()) {
    throw new Error('Invalid date provided to formatCurTimeISO')
  }
  return dayjsDate.tz(TIMEZONE).toISOString()
}

/**
 * 获取当前时间
 * @returns 当前时区时间的 Date 对象
 */
export function getTime(date: dayjs.ConfigType = null): Date {
    return getDayJs(date).toDate()
}

export function getDayJs(date: dayjs.ConfigType = null): Dayjs {
  const dayjsDate = date ? dayjs(date) :dayjs()
  if (!dayjsDate.isValid()) {
    throw new Error(`Invalid date provided to getCurDayJs: ${date}`)
  }
  return dayjsDate.tz(TIMEZONE).add(0)
}

/**
 * 判断给定日期是否是今天（时区） 
 * @param date 要比较的日期
 * @returns 如果是今天返回 true，否则返回 false
 */
export function isToday(date: Date | string | number): boolean {
  try {
    const targetDate = getDayJs(date)
    const today = getDayJs()
    return targetDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD')
  } catch (error) {
    return false
  }
}

/**
 * 判断给定日期是否是之前的日期（时区）
 * @param date 要比较的日期
 * @returns 如果是之前的日期返回 true，否则返回 false
 */
export function isPreviousDate(date: Date | string | number): boolean {
  try {
    const targetDate = getDayJs(date)
    const today = getDayJs()
    return targetDate.isBefore(today, 'day')
  } catch (error) {
    return false
  }
}

/**
 * 判断给定日期是否是未来的日期（时区）
 * @param date 要比较的日期
 * @returns 如果是未来的日期返回 true，否则返回 false
 */
export function isFutureDate(date: Date | string | number): boolean {
  try {
    const targetDate = getDayJs(date)
    const today = getDayJs()
    return targetDate.isAfter(today, 'day')
  } catch (error) {
    return false
  }
}

/**
 * 判断给定日期是否是未来的日期（时区）
 * @param date 要比较的日期
 * @returns 如果是未来的日期返回 true，否则返回 false
 */
export function isTodayOrFutureDate(date: Date | string | number): boolean {
  try {
    const targetDate = getDayJs(date)
    const today = getDayJs()
    return targetDate.isSame(today, 'day') || targetDate.isAfter(today, 'day')
  } catch (error) {
    return false
  }
}

/**
 * 获取两个日期之间的天数差（时区）
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 天数差（date1 - date2），正数表示 date1 在 date2 之后
 */
export function getDaysDifference(date1: Date | string | number, date2: Date | string | number): number {
  try {
    const d1 = getDayJs(date1)
    const d2 = getDayJs(date2)
    return d1.diff(d2, 'day')
  } catch (error) {
    return 0
  }
}

export function getDaysDifferenceHour(date1: Date | string | number, date2: Date | string | number): number {
  try {
    const d1 = getDayJs(date1)
    const d2 = getDayJs(date2)
    return d1.diff(d2, 'hour')
  } catch (error) {
    return 0
  }
}
