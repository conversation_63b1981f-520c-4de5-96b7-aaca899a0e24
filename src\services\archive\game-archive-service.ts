import { eq, and, desc, sql } from 'drizzle-orm'

import { getDb, getReadDb } from '@/db'
import { getRedis } from '@/redis'
import { gameArchives } from '@/schema'
import  logger  from '@/lib/logger'
import get from 'lodash/get'
import set from 'lodash/set'
import isEqual from 'lodash/isEqual'
import isArrayLikeObject from 'lodash/isArrayLikeObject'
import cloneDeep from 'lodash/cloneDeep'
import { randomUUID } from 'crypto'


export interface GameArchive {
  id: number
  userId: string
  gameId: string
  archiveData: any
  version: number
  size: number
  lastPlayed?: Date
  createdAt: Date
  updatedAt: Date
}

export interface CreateArchiveRequest {
  userId: string
  gameId: string
  archiveId?: string // 可选，前端可传递自定义ID，不传则自动生成UUID
  archiveData: any
  lastPlayed?: Date
  userCreatedAt?: Date // 用户创建时间，用于分区优化
}

export interface UpdateArchiveRequest {
  // 更新模式：patch=增量更新，replace=全量替换
  updateMode?: 'patch' | 'replace'

  // 存档数据：
  // - replace模式：完全替换整个存档数据
  // - patch模式：存在的字段替换，不存在的字段保持存档原来的值
  archiveData?: Record<string, any>

  lastPlayed?: Date
  immediate?: boolean // 是否立即写入数据库（付费相关等重要操作）
  priority?: 'high' | 'normal' | 'low' // 队列优先级
  clientVersion?: string // 客户端版本号，用于冲突检测
  operationLog?: Array<{ // 操作日志，用于验证数据变化的合法性
    amount?: any // 变化数量
    source?: string // 来源：quest_reward, purchase, battle_win等
    timestamp: number // 操作时间戳
  }>
}

import { archiveQueue, QueueTask } from '@/services/queue/archive-queue'
import { json } from 'zod'
import { getTime } from '@/utils/time'

export class GameArchiveService {
  private readonly CACHE_PREFIX = 'game_archive:'
  private readonly CACHE_TTL = 3600 // 1小时缓存
  private readonly MAX_ARCHIVE_SIZE = 1024 * 1024 * 10 // 1MB 最大存档大小
  private readonly MAX_UPDATE_FIELDS = 300 // 单次更新最大字段数

  /**
   * 验证更新数据的合法性和安全性
   */
  private validateUpdateData(updateData: Record<string, any>): { valid: boolean; message?: string } {
    try {
      // 检查数据大小
      // const dataSize = JSON.stringify(updateData).length
      // if (dataSize > this.MAX_ARCHIVE_SIZE) {
      //   return { valid: false, message: `更新数据过大: ${dataSize} bytes, 最大允许: ${this.MAX_ARCHIVE_SIZE} bytes` }
      // }

      // 检查字段数量
      // const fieldCount = this.countFields(updateData)
      // if (fieldCount > this.MAX_UPDATE_FIELDS) {
      //   return { valid: false, message: `更新字段过多: ${fieldCount}, 最大允许: ${this.MAX_UPDATE_FIELDS}` }
      // }

      // 检查危险字段
      const dangerousFields = ['__proto__', 'constructor', 'prototype']
      if (this.containsDangerousFields(updateData, dangerousFields)) {
        return { valid: false, message: '包含危险字段，更新被拒绝' }
      }

      return { valid: true }
    } catch (error) {
      return { valid: false, message: `数据验证失败: ${error.message}` }
    }
  }

  /**
   * 递归计算对象字段数量
   */
  private countFields(obj: any, depth = 0): number {
    if (depth > 10 || !obj || typeof obj !== 'object') return 0

    let count = 0
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        count++
        if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
          count += this.countFields(obj[key], depth + 1)
        }
      }
    }
    return count
  }

  /**
   * 检查是否包含危险字段
   */
  private containsDangerousFields(obj: any, dangerousFields: string[], depth = 0): boolean {
    if (depth > 10 || !obj || typeof obj !== 'object') return false

    for (const key in obj) {
      if (dangerousFields.includes(key)) return true
      if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        if (this.containsDangerousFields(obj[key], dangerousFields, depth + 1)) return true
      }
    }
    return false
  }

  /**
   * 应用增量更新到存档数据
   * 存在的字段替换，不存在的字段保持存档原来的值
   * 性能优化：使用浅拷贝 + 选择性深拷贝，避免不必要的对象克隆
   * 可靠性优化：严格的类型检查和错误处理
   */
  private applyIncrementalUpdate(originalData: Record<string, any>, updateData: Record<string, any>): Record<string, any> {
    // 性能优化：使用浅拷贝作为基础，只对需要修改的部分进行深拷贝
    const result = { ...originalData }

    // 递归合并对象，优化性能和可靠性
    function mergeDeep(target: any, source: any, depth = 0): any {
      // 防止过深的递归（安全性考虑）
      if (depth > 10) {
        logger.warn('增量更新递归深度过深，停止合并', { depth })
        return target
      }

      for (const key in source) {
        if (!source.hasOwnProperty(key)) continue

        const sourceValue = source[key]

        // 处理 null 和 undefined
        if (sourceValue === null || sourceValue === undefined) {
          target[key] = sourceValue
          continue
        }

        // 处理数组：直接替换（避免复杂的数组合并，提高性能和可预测性）
        if (Array.isArray(sourceValue)) {
          target[key] = sourceValue.slice() // 浅拷贝数组
          continue
        }

        // 处理对象：递归合并
        if (typeof sourceValue === 'object') {
          // 如果目标不是对象或不存在，创建新对象
          if (!target[key] || typeof target[key] !== 'object' || Array.isArray(target[key])) {
            target[key] = {}
          } else {
            // 只有在需要修改时才进行浅拷贝
            target[key] = { ...target[key] }
          }
          mergeDeep(target[key], sourceValue, depth + 1)
          continue
        }

        // 处理基本类型：直接替换
        target[key] = sourceValue
      }

      return target
    }

    try {
      const mergedResult = mergeDeep(result, updateData)

      // 性能监控日志
      logger.debug('增量更新完成', {
        originalSize: JSON.stringify(originalData).length,
        updateSize: JSON.stringify(updateData).length,
        resultSize: JSON.stringify(mergedResult).length,
        originalKeys: Object.keys(originalData).length,
        updateKeys: Object.keys(updateData).length
      })

      return mergedResult
    } catch (error) {
      logger.error('增量更新失败，回退到深拷贝方式', { error: error.message })
      // 回退策略：使用深拷贝确保数据安全
      const fallbackResult = cloneDeep(originalData)
      return { ...fallbackResult, ...updateData }
    }
  }

  /**
   * 生成缓存键
   * 包含存档ID的完整缓存键
   */
  private getCacheKey(userId: string, gameId: string, archiveId: string): string {
    return `${this.CACHE_PREFIX}${userId}:${gameId}:${archiveId}`
  }

  /**
   * 获取用户创建时间（用于分区查询优化）
   */
  private async getUserCreatedAt(userId: string): Promise<Date> {
    try {
      // 尝试从用户服务获取用户创建时间
      const { userService } = await import('@/services/user/user-service')
      const userProfile = await userService.getUserProfile(userId)
      return userProfile.createdAt
    } catch (error) {
      // 如果获取失败，使用当前时间
      logger.warn('获取用户创建时间失败，使用当前时间', { userId, message: error.message })
      return getTime()
    }
  }


  /**
   * 直接更新数据库中的存档（队列任务使用）
   */
  async updateArchiveInDatabase(
    userId: string,
    gameId: string,
    updateData: UpdateArchiveRequest
  ): Promise<void> {
    try {
      const db = await getDb()

      // 根据更新模式处理存档数据
      let finalArchiveData: Record<string, any>

      if (updateData.updateMode === 'patch' && updateData.archiveData) {
        // 增量更新模式：需要先获取当前数据
        const [currentArchive] = await db
          .select({ archiveData: gameArchives.archiveData })
          .from(gameArchives)
          .where(and(
            eq(gameArchives.userId, userId),
            eq(gameArchives.gameId, gameId)
          ))
          .limit(1)

        if (!currentArchive) {
          throw new Error('存档不存在，无法执行增量更新')
        }

        // 应用增量更新
        finalArchiveData = this.applyIncrementalUpdate(currentArchive.archiveData, updateData.archiveData)
        logger.debug('数据库增量更新', {
          userId,
          gameId,
          updateFields: Object.keys(updateData.archiveData).length
        })
      } else if (updateData.archiveData) {
        // 全量替换模式
        finalArchiveData = updateData.archiveData
        logger.debug('数据库全量更新', { userId, gameId })
      } else {
        throw new Error('缺少更新数据：需要提供 archiveData')
      }

      await db
        .update(gameArchives)
        .set({
          archiveData: finalArchiveData,
          lastPlayed: updateData.lastPlayed,
          version: sql`${gameArchives.version} + 1`,
          updatedAt: getTime(),
        })
        .where(and(
          eq(gameArchives.userId, userId),
          eq(gameArchives.gameId, gameId)
        ))

      logger.debug('数据库存档更新成功', { userId, gameId })
    } catch (error) {
      logger.error('数据库存档更新失败', { message: error.message, userId, gameId })
      throw error
    }
  }

  /**
   * 创建游戏存档
   */
  async createArchive(request: CreateArchiveRequest): Promise<GameArchive | null> {
    try {
      const db = await getDb()
      const redis = await getRedis()

      // 检查是否已有存档
      const existingArchive = await this.getArchiveByUserGame(request.userId, request.gameId)
      if (existingArchive) {
        logger.warn('存档已存在，跳过创建', { userId: request.userId, gameId: request.gameId })
        return null
      }

      // 获取用户创建时间（分区键）
      const userCreatedAt = request.userCreatedAt || await this.getUserCreatedAt(request.userId)
      const archiveData = request.archiveData

      // 如果前端没有传递archiveId，则生成UUID
      const archiveId = request.archiveId || randomUUID()

      const [archive] = await db.insert(gameArchives).values({
        id: archiveId,
        userId: request.userId,
        gameId: request.gameId,
        archiveData,
        lastPlayed: request.lastPlayed,
        userCreatedAt, // 分区键
        updatedAt: getTime(),
      }).returning()

      // 缓存存档数据
      const cacheKey = this.getCacheKey(request.userId, request.gameId, archive.id)
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify({
        ...archive,
        archiveData: request.archiveData, // 缓存原始数据
      }))

      logger.info('游戏存档创建成功', {
        archiveId: archive.id,
        userId: request.userId,
        gameId: request.gameId,
      })

      return {
        ...archive,
        archiveData: request.archiveData,
      }
    } catch (error) {
      logger.error('创建游戏存档失败', {
        message: error.message,
        userId: request.userId,
        gameId: request.gameId,
      })
      throw error
    }
  }
/**
   * 验证操作日志的合法性（防止内存修改）
   */
  private validateOperationLog(
    oldData: any,
    newData: any,
    operationLog: Array<{
      amount?: any
      source?: string
      path?:string
      timestamp?: number
    }>
  ): { valid: boolean; message?: string } {
    try {
      const simulatedData = JSON.parse(JSON.stringify(oldData))
    try{
        for (const operation of operationLog) {
          if(!operation.path) operation.path = ""
          if(operation.path !== "" && isArrayLikeObject(simulatedData[operation.source])){
            set(simulatedData[operation.source],operation.path, operation.amount)
          }else{
            simulatedData[operation.source] = operation.amount
          }
          // switch (typeof operation.amount) {
          //   case 'number':
          //     // if(operation.get){
          //     //   set(simulatedData,operation.source, (get(oldData,operation.source) || 0) + operation.amount)
          //     // } else{
          //     //   simulatedData[operation.source] = (oldData[operation.source] || 0) + operation.amount
          //     // }          

          //     break
          //   case 'string':
          //     if(operation.path == ""){
          //       simulatedData[operation.source] = operation.amount
          //     }else{
          //       set(simulatedData[operation.source],operation.path, operation.amount)
          //     }                
          //     break
          //   case 'boolean':
          //     if(operation.path == ""){
          //       simulatedData[operation.source] = operation.amount
          //     }else{
          //       set(simulatedData[operation.source],operation.path, operation.amount)
          //     }                
          //     break 
          // }
        } 
      } catch(error)
      {
        logger.error('操作日志验证失败', { message: error })
          return { valid: false, message: '操作日志验证过程出错'+ error }
      }
      const keyFields = Object.keys(newData)
      for (const field of keyFields) {
        if (isEqual(simulatedData[field], newData[field])) {
          return {
            valid: false,
            message: `${field} 数据不一致: 期望 ${simulatedData[field]}, 实际 ${newData[field]}`
          }
        }
      }

      return { valid: true }
    } catch (error) {
      console.log(error)
      logger.error('操作日志验证失败', { message: error.message })
      return { valid: false, message: '操作日志验证过程出错' }
    }
  }
  /**
   * 获取存档详情
   */
  async getArchiveByArchiveId(userId: string, gameId: string, archiveId: string): Promise<GameArchive | null> {
    try {
      const redis = await getRedis()
      const cacheKey = this.getCacheKey(userId, gameId, archiveId)

      // 尝试从缓存获取
      const cached = await redis.get(cacheKey)
      if (cached) {
        logger.debug('从缓存获取存档', { archiveId, userId, gameId })
        const cachedStr = typeof cached === 'string' ? cached : String(cached)
        return JSON.parse(cachedStr)
      }

      // 从数据库获取
      const db = await getReadDb()
      const [archive] = await db
        .select()
        .from(gameArchives)
        .where(and(
          eq(gameArchives.id, archiveId),
          eq(gameArchives.userId, userId),
          eq(gameArchives.gameId, gameId)
        ))

      if (!archive) {
        return null
      }
      const result = {
        ...archive,
        archiveData:archive.archiveData,
      }

      // 缓存结果
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(result))

      logger.debug('从数据库获取存档', { archiveId, userId, gameId })
      return result
    } catch (error) {
      logger.error('获取游戏存档失败', {
        message: error.message,
        archiveId,
        userId,
        gameId,
      })
      throw error
    }
  }

  /**
   * 根据用户ID和游戏ID获取存档
   */
  async getArchiveByUserGame(userId: string, gameId: string): Promise<GameArchive | null> {
    try {
      const db = await getReadDb()
      const redis = await getRedis()

      // 从数据库查找存档（因为需要archiveId来生成缓存键）
      const [archive] = await db
        .select()
        .from(gameArchives)
        .where(and(
          eq(gameArchives.userId, userId),
          eq(gameArchives.gameId, gameId)
        ))
        .limit(1)

      if (!archive) {
        return null
      }

      // 生成缓存键
      const cacheKey = this.getCacheKey(userId, gameId, archive.id)

      // 尝试从缓存获取
      const cached = await redis.get(cacheKey)
      if (cached) {
        const cachedStr = typeof cached === 'string' ? cached : String(cached)
        const cachedArchive = JSON.parse(cachedStr)
        logger.debug('从缓存获取存档', { userId, gameId, archiveId: archive.id })
        return cachedArchive
      }

      const result = {
        ...archive,
        archiveData: archive.archiveData,
      }

      // 缓存结果
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(result))

      logger.debug('从数据库获取存档', { userId, gameId, archiveId: archive.id })
      return result
    } catch (error) {
      logger.error('获取存档失败', {
        message: error.message,
        userId,
        gameId,
      })
      throw error
    }
  }



  /**
   * 更新游戏存档（支持缓存优先和队列机制）
   */
  async updateArchive(
    userId: string,
    gameId: string,
    request: UpdateArchiveRequest
  ): Promise<GameArchive | null> {
    try {
      const redis = await getRedis()
      // 数据验证
      if (request.archiveData) {
        const validation = this.validateUpdateData(request.archiveData)
        if (!validation.valid) {
          throw new Error(`数据验证失败: ${validation.message}`)
        }
      }

      if (request.archiveData && request.immediate) {
        const validation = await this.validateClientData(userId, gameId,  request.archiveData, request.clientVersion, request.operationLog)
        if (!validation.valid) {
          throw new Error(`数据验证失败: ${validation.message}`)
        }
      }

      // 如果是立即更新（付费相关等重要操作）
      if (request.immediate) {
        logger.info('执行立即更新', { userId, gameId })
        // 先刷新该用户该游戏的所有队列任务
        await archiveQueue.flushQueue(
          async (task) => await this.updateArchiveInDatabase(task.userId, task.gameId, task.data),
          userId,
          gameId
        )

        // 然后立即更新数据库
        return await this.updateArchiveImmediately(userId, gameId, request)
      }

      // immediate=false: 只更新缓存，不直接更新数据库，通过队列异步处理
      // 1. 先获取当前存档
      let currentArchive = await this.getArchiveByUserGame(userId, gameId)
      if (!currentArchive) {
        return null
      }

      // 2. 根据更新模式处理存档数据
      let updatedArchiveData: Record<string, any>

      if (request.updateMode === 'patch' && request.archiveData) {
        // 增量更新模式
        logger.debug('执行增量更新', {
          userId,
          gameId,
          updateFields: Object.keys(request.archiveData).length
        })
        updatedArchiveData = this.applyIncrementalUpdate(currentArchive.archiveData, request.archiveData)
      } else if (request.archiveData) {
        // 全量替换模式
        logger.debug('执行全量更新', { userId, gameId })
        updatedArchiveData = request.archiveData
      } else {
        throw new Error('缺少更新数据：需要提供 archiveData')
      }

      // 3. 创建更新后的存档对象（仅用于缓存，不直接写入数据库）
      const updatedArchive: GameArchive = {
        ...currentArchive,
        archiveData: updatedArchiveData,
        lastPlayed: request.lastPlayed || currentArchive.lastPlayed,
        version: currentArchive.version + 1,
        updatedAt: getTime(),
        // 暂时保持原有的压缩状态，队列处理时会重新计算
        size: JSON.stringify(updatedArchiveData).length, // 临时大小
      }

      // 4. 立即更新缓存
      const cacheKey = this.getCacheKey(userId, gameId, String(updatedArchive.id))

      // 性能监控：记录缓存操作时间
      const cacheStart = Date.now()

      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(updatedArchive))

      const cacheTime = Date.now() - cacheStart

      // 5. 添加到队列进行异步数据库更新（不立即写入数据库）
      const queueTask: QueueTask = {
        id: `update_${userId}_${gameId}_${Date.now()}`,
        type: 'update',
        userId,
        gameId,
        data: request,
        priority: request.priority || 'normal',
        timestamp: Date.now(),
        retries: 0,
      }

      await archiveQueue.addTask(queueTask)

      setImmediate(() => archiveQueue.processQueue(
        async (task) => await this.updateArchiveInDatabase(task.userId, task.gameId, task.data)
      ))

      logger.info('存档缓存更新完成，已加入队列（数据库将异步更新）', {
        userId,
        gameId,
        updateMode: request.updateMode,
        priority: queueTask.priority,
        immediate: false,
        cacheTime,
        archiveSize: JSON.stringify(updatedArchiveData).length,
        version: updatedArchive.version,
        note: '数据已更新到缓存，数据库更新将通过队列异步处理'
      })

      return updatedArchive
    } catch (error) {
      logger.error('更新游戏存档失败', {
        message: error.message,
        userId,
        gameId,
      })
      throw error
    }
  }
  /**
     * 验证客户端数据的合法性
     */
  private async validateClientData(
    userId: string,
    gameId: string,
    newData: any,
    clientVersion?: string,
    operationLog?: Array<any>
  ): Promise<{ valid: boolean; message?: string; serverData?: any }> {
    try {
      // 获取服务器端的当前数据
      const serverArchive = await this.getArchiveByUserGame(userId, gameId)
      if (!serverArchive) {
        return { valid: false, message: '存档不存在' }
      }

     
      const logValidation = this.validateOperationLog(serverArchive.archiveData, newData, operationLog)
      if (!logValidation.valid) {
      return {
            valid: false,
            message: logValidation.message,
            serverData: serverArchive.archiveData
          }
        }
      
      return { valid: true }
    } catch (error) {
      logger.error('客户端数据验证失败', { message: error.message, userId, gameId })
      return { valid: false, message: '验证过程出错' }
    }
  }
  /**
   * 立即更新存档（付费相关等重要操作）
   */
  private async updateArchiveImmediately(
    userId: string,
    gameId: string,
    request: UpdateArchiveRequest
  ): Promise<GameArchive | null> {
    try {
      const db = await getDb()
      const redis = await getRedis()

      // 根据更新模式处理存档数据
      let finalArchiveData: Record<string, any>

      if (request.updateMode === 'patch' && request.archiveData) {
        // 增量更新模式：需要先获取当前数据
        const [currentArchive] = await db
          .select({ archiveData: gameArchives.archiveData })
          .from(gameArchives)
          .where(and(
            eq(gameArchives.userId, userId),
            eq(gameArchives.gameId, gameId)
          ))
          .limit(1)

        if (!currentArchive) {
          throw new Error('存档不存在，无法执行增量更新')
        }

        // 应用增量更新
        finalArchiveData = this.applyIncrementalUpdate(currentArchive.archiveData, request.archiveData)
        logger.debug('立即增量更新', {
          userId,
          gameId,
          updateFields: Object.keys(request.archiveData).length
        })
      } else if (request.archiveData) {
        // 全量替换模式
        finalArchiveData = request.archiveData
        logger.debug('立即全量更新', { userId, gameId })
      } else {
        throw new Error('缺少更新数据：需要提供 archiveData')
      }

      const [updatedArchive] = await db
        .update(gameArchives)
        .set({
          archiveData: finalArchiveData,
          lastPlayed: request.lastPlayed,
          version: sql`${gameArchives.version} + 1`,
          updatedAt: getTime(),
        })
        .where(and(
          eq(gameArchives.userId, userId),
          eq(gameArchives.gameId, gameId)
        ))
        .returning()

      if (!updatedArchive) {
        return null
      }

      const result = {
        ...updatedArchive,
        archiveData: finalArchiveData,
      }

      // 更新缓存
      const cacheKey = this.getCacheKey(userId, gameId, String(updatedArchive.id))
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(result))

      logger.info('游戏存档更新成功', {
        archiveId: updatedArchive.id,
        userId,
        gameId,
        version: updatedArchive.version,
      })

      return result
    } catch (error) {
      logger.error('更新游戏存档失败', {
        message: error.message,
        userId,
        gameId,
      })
      throw error
    }
  }

  /**
   * 删除游戏存档
   */
  async deleteArchive(userId: string, gameId: string): Promise<boolean> {
    try {
      const db = await getDb()
      const redis = await getRedis()

      const [deletedArchive] = await db
        .delete(gameArchives)
        .where(and(
          eq(gameArchives.userId, userId),
          eq(gameArchives.gameId, gameId)
        ))
        .returning({ id: gameArchives.id })

      if (!deletedArchive) {
        return false
      }

      // 清除缓存
      const cacheKey = this.getCacheKey(userId, gameId, String(deletedArchive.id))
      await redis.del(cacheKey)

      logger.info('游戏存档删除成功', { archiveId: deletedArchive.id, userId, gameId })
      return true
    } catch (error) {
      logger.error('删除游戏存档失败', {
        message: error.message,
        userId,
        gameId,
      })
      throw error
    }
  }


}

// 导出单例实例
export const gameArchiveService = new GameArchiveService()
