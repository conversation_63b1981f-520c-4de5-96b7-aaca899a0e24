import { validator } from 'hono-openapi/zod'
import z from 'zod'
import { getDb } from '@/db'
import { createRouter } from '@/lib/create-app'
import { jwtAuth } from '@/lib/jwt'
import { eventData } from '@/schema'
import { createResponse } from '@/services'
import { globalResponse, jsonSchema } from '@/types'
import { mailListResponseSchema } from '@/types/mail'
import { gameIdSchema, idSchema, uidSchema } from '@/types/user'

const router = createRouter()

router.post('/event', jwtAuth(), globalResponse({
  description: '打点',
  tags: ['记录'],
}), validator('json', z.object({
  eventCode: z.string().meta({
    example: 'level_up',
    description: '事件代码',
  }),
  gameId: gameIdSchema,
  data: jsonSchema.meta({
    example: {
      level: 10,
    },
    description: '事件数据（JSON格式）',
  }),
})), async (c) => {
  try {
    const { eventCode, gameId, data } = c.req.valid('json')
    const user = c.get('user')
    const { id, uid } = user

    const db = await getDb()
    await db.insert(eventData).values({
      userId: id,
      gameId,
      uid,
      eventCode,
      data,
    })
    return c.json(createResponse('打点成功'))
  }
  catch (error) {
    console.error('打点失败', { error: error.message })
    return c.json(createResponse(error.message, false), 500)
  }
})

export default router
