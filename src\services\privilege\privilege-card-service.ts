import { and, desc, eq, gte, sql } from 'drizzle-orm'
import { getDb, getReadDb } from '@/db'
import { getRedis } from '@/redis'
import {
  privilegeRecords,
  userProfiles,
  userAdInfo,
} from '@/schema'
import { AD_THRESHOLDS, REWARD_CONFIG } from '@/config'
import { formatCurTime, getDaysDifference, getTime, isToday, isTodayOrFutureDate } from '@/utils/time'
import { rewardType } from '@/types/privilege'

// 特权卡类型
export type rewardType = typeof rewardType[keyof typeof rewardType]





export class PrivilegeCardService {
  private readonly CACHE_PREFIX = 'privilege:'
  private readonly CACHE_TTL = 3600

  private getCacheKey(userId: string): string {
    return `${this.CACHE_PREFIX}${userId}`
  }

  /**
   * 获取用户特权状态
   */
  async getUserPrivilegeStatus(userId: string): Promise<any> {
    try {
      const db = await getReadDb()
      const now = getTime()
      const today = formatCurTime(now)
      // 获取用户资料 + 广告观看信息（左连接 user_ad）
      const [row] = await db
        .select()
        .from(userProfiles)
        .leftJoin(userAdInfo, eq(userProfiles.id, userAdInfo.userId))
        .where(eq(userProfiles.id, userId))
        .limit(1)
      const userProfile = row?.user_profiles
      const adInfo = row?.user_ad

      // 获取激活的记录（通过时间计算）
      const activeRecords = await db
        .select()
        .from(privilegeRecords)
        .where(and(
          eq(privilegeRecords.userId, userId),
          eq(privilegeRecords.status, 'active'),
          gte(privilegeRecords.endDate, now),
        ))

      // 分类记录并处理多条记录的情况
      const monthlyCards = activeRecords.filter((r: any) => r.rewardType === rewardType.monthly)
      const weeklyCards = activeRecords.filter((r: any) => r.rewardType === rewardType.weekly)
      const trialCards = activeRecords.filter((r: any) => r.rewardType === rewardType.trial)

      // 对于每种类型，选择最晚到期的记录作为主记录，检查是否有任何记录今日已领取
      const getCardStatus = (cards: any[]) => {
        if (cards.length === 0) {
          return { active: false, claimed: false, days: 0 }
        }

        // 选择最晚到期的记录
        const latestCard = cards.reduce((latest, current) => {
          return new Date(current.endDate) > new Date(latest.endDate) ? current : latest
        })

        // 检查是否有任何记录今日已领取
        const claimed = cards.some(card => {
          const claimDates = (card.claimDates as string[]) || []
          return claimDates.some(date => isToday(date))
        })

        return {
          active: true,
          claimed,
          days: getDaysDifference(latestCard.endDate, now),
        }
      }

      const monthly = getCardStatus(monthlyCards)
      const weekly = getCardStatus(weeklyCards)
      const trial = getCardStatus(trialCards)

      return {
        userId,
        // 卡片状态（通过时间计算）
        monthly,
        weekly,
        trial,
        // 兼容旧字段命名
        monthlyCardActive: monthly.active,
        monthlyTrialActive: trial.active,
        // 其他状态（来自 user_ad 表）
        todayAdWatchCount: adInfo?.todayAdWatchCount || 0,
        lastUpdated: adInfo?.updatedAt ?? userProfile?.updatedAt ?? null,
      }
    }
    catch (error) {
      console.error('获取用户特权状态失败', error)
      throw error
    }
  }

  /**
   * 记录广告观看
   */
  async recordAdWatch(userId: string): Promise<{ canTriggerTrial: boolean, canTriggerDiscount: boolean ,todayAdWatchCount:number }> {
    try {
      const db = await getDb()

      // 更新广告观看表中的观看次数与时间
      await db
        .update(userAdInfo)
        .set({
          todayAdWatchCount: sql`${userAdInfo.todayAdWatchCount} + 1`,
          lastAdWatchAt: getTime(),
          updatedAt: getTime(),
        })
        .where(eq(userAdInfo.userId, userId))

      // 获取当前状态
      const status = await this.getUserPrivilegeStatus(userId)
      const watchCount = status.todayAdWatchCount
      // const now = getTime()
      // 检查用户是否为首日用户
      // const [userProfile] = await db
      //   .select()
      //   .from(userProfiles)
      //   .where(eq(userProfiles.id, userId))
      //   .limit(1)
      // const cardRecords = await db
      //   .select()
      //   .from(privilegeRecords)
      //   .where(and(
      //     eq(privilegeRecords.userId, userId),
      //     // eq(privilegeRecords.rewardType, request.rewardType),
      //     eq(privilegeRecords.status, 'active'),
      //     gte(privilegeRecords.startDate, now),
      //   ))
      //   .orderBy(desc(privilegeRecords.endDate))
      // 检查触发条件
      const canTriggerTrial = watchCount >= AD_THRESHOLDS.trial
        && !status.monthlyTrialActive
        // && cardRecords.filter(i => i.rewardType === rewardType.monthly).length === 0

      const canTriggerDiscount = watchCount >= AD_THRESHOLDS.monthlyDiscount
        && !status.monthlyCardActive
      if(canTriggerTrial){
        await this.startTrial(userId)
      }
      return { canTriggerTrial, canTriggerDiscount, todayAdWatchCount: status.todayAdWatchCount }
    }
    catch (error) {
      console.error('记录广告观看失败', error)
      throw error
    }
  }

  /**
   * 开始试用
   */
  async startTrial(userId: string): Promise<any> {
    try {
      const db = await getDb()
      // const redis = await getRedis()

      // 检查用户是否为首日用户
      const [userProfile] = await db
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.id, userId))
        .limit(1)

      // if (!userProfile || !this.isFirstDay(userProfile.createdAt)) {
      //   throw new Error('试用功能仅限首日用户')
      // }

      const startTime = getTime()
      const endTime = getTime()
      endTime.setHours(endTime.getHours() + 24) // 24小时试用

      // 创建试用记录
      await db.insert(privilegeRecords).values({
        userId,
        gameId: 'privilege',
        rewardType: rewardType.trial,
        status: 'active',
        purchasePrice: '0',
        originalPrice: '0',
        startDate: startTime,
        endDate: endTime,
        purchaseSource: '',
      }).returning()

      // 清除缓存
      // await redis.del(this.getCacheKey(userId))

      return { userId, startTime, endTime, status: 'active' }
    }
    catch (error) {
      console.error('开始试用失败', error)
      throw error
    }
  }

  // /**
  //  * 触发折扣
  //  */
  // async triggerDiscount(userId: string): Promise<any> {
  //   try {
  //     const db = await getDb()
  //     const redis = await getRedis()

  //     const now = getTime()
  //     const endTime = getTime()
  //     endTime.setHours(endTime.getHours() + 1) // 1小时折扣

  //     // 创建折扣记录
  //     await db.insert(privilegeRecords).values({
  //       userId,
  //       gameId: 'privilege',
  //       rewardType: rewardType.monthly,
  //       status: 'active',
  //       purchasePrice: PRICE_CONFIG.monthly.discount.toString(),
  //       originalPrice: PRICE_CONFIG.monthly.original.toString(),
  //       discountPercent: PRICE_CONFIG.monthly.discountPercent,
  //       startDate: now,
  //       endDate: endTime,
  //     }).returning()

  //     // 更新用户资料中的折扣状态
  //     await db
  //       .update(userProfiles)
  //       .set({
  //         updatedAt: getTime(),
  //       })
  //       .where(eq(userProfiles.id, userId))

  //     // 清除缓存
  //     await redis.del(this.getCacheKey(userId))

  //     return {
  //       userId,
  //       discountPercent: PRICE_CONFIG.monthly.discountPercent,
  //       originalPrice: PRICE_CONFIG.monthly.original,
  //       discountPrice: PRICE_CONFIG.monthly.discount,
  //       startTime: now,
  //       endTime,
  //       status: 'active',
  //     }
  //   }
  //   catch (error) {
  //     console.error('触发折扣失败', error)
  //     throw error
  //   }
  // }

  /**
   * 购买特权卡
   */
  async purchaseCard(request: {
    userId: string
    rewardType: rewardType
    purchasePrice: number
    originalPrice: number
    purchaseSource?: string
    transactionId?: string
    gameId?: string
  }): Promise<any> {
    try {
      const db = await getDb()
      const redis = await getRedis()
      const now = getTime()

      // 检查是否已有激活的同类型卡，获取最晚的结束时间
      const existingCards = await db
        .select()
        .from(privilegeRecords)
        .where(and(
          eq(privilegeRecords.userId, request.userId),
          eq(privilegeRecords.rewardType, request.rewardType),
          eq(privilegeRecords.status, 'active'),
          gte(privilegeRecords.endDate, now),
        ))
        .orderBy(desc(privilegeRecords.endDate))

      // 计算开始和结束时间
      let startDate: Date
      let endDate: Date

      if (existingCards.length > 0) {
        // 有激活的卡，从最晚结束时间开始计算
        startDate = getTime(existingCards[0].endDate)
        endDate = getTime(startDate)
      }
      else {
        // 没有激活的卡，从当前时间开始
        startDate = now
        endDate = getTime(startDate)
      }

      // 根据卡片类型添加时长
      if (request.rewardType === rewardType.monthly) {
        endDate.setDate(endDate.getDate() + 30)
      }
      else {
        endDate.setDate(endDate.getDate() + 7)
      }

      // 创建购买记录
      const [card] = await db.insert(privilegeRecords).values({
        userId: request.userId,
        gameId: request.gameId || 'privilege',
        rewardType: request.rewardType,
        purchasePrice: request.purchasePrice.toString(),
        originalPrice: request.originalPrice.toString(),
        startDate,
        endDate,
        purchaseSource: request.purchaseSource || 'normal',
        transactionId: request.transactionId,
      }).returning()

      // 清除缓存
      await redis.del(this.getCacheKey(request.userId))

      console.log('特权卡购买成功', {
        userId: request.userId,
        rewardType: request.rewardType,
        startDate,
        endDate,
        isExtension: existingCards.length > 0,
      })

      return card
    }
    catch (error) {
      console.error('购买特权卡失败', error)
      throw error
    }
  }

  /**
   * 领取奖励
   */
  async claimReward(userId: string, priType: string): Promise<any> {
    try {
      const db = await getDb()
      const redis = await getRedis()

      const now = getTime()
      const today = formatCurTime(now)
      const rewards = REWARD_CONFIG[priType].daily
    

      // 查找所有激活的同类型记录
      const activeRecords = await db
        .select()
        .from(privilegeRecords)
        .where(and(
          eq(privilegeRecords.userId, userId),
          eq(privilegeRecords.status, 'active'),
          eq(privilegeRecords.rewardType, priType),
          gte(privilegeRecords.endDate, now)
        ))

      if (activeRecords.length === 0) {
        throw new Error(`没有激活的${priType === rewardType.trial ? '试用' : '特权卡'}`)
      }

      // 检查是否已经有任何一条记录今日已领取
      const hasClaimedToday = activeRecords.some(record => {
        const claimDates = (record.claimDates as string[]) || []
        return claimDates.some(date => isToday(date))
      })

      if (hasClaimedToday) {
        throw new Error('今日奖励已领取')
      }

      // 选择最早到期的记录进行更新（优先使用即将到期的）
      const targetRecord = activeRecords.reduce((earliest, current) => {
        return new Date(current.endDate) < new Date(earliest.endDate) ? current : earliest
      })

      // 获取当前的领取日期数组
      const currentClaimDates = (targetRecord.claimDates as string[]) || []

      // 添加今日日期到数组
      const newClaimDates = [...currentClaimDates, today]

      // 更新记录
      const [updatedRecord] = await db
        .update(privilegeRecords)
        .set({
          claimDates: newClaimDates as any,
          updatedAt: now
        })
        .where(eq(privilegeRecords.id, targetRecord.id))
        .returning()

      // 清除缓存
      await redis.del(this.getCacheKey(userId))

      return {
        // id: updatedRecord?.id,
        userId,
        rewardType: priType,
        claimDate: now,
        rewards
      }
    }
    catch (error) {
      console.error('领取奖励失败', error)
      throw error
    }
  }

  /**
   * 获取可领取奖励
   */
  async getClaimableRewards(userId: string): Promise<any[]> {
    try {
      const db = await getReadDb()
      const now = getTime()
      const today = formatCurTime(now)  // 使用统一的时间格式
      const rewards = []

      // 获取激活的记录
      const activeRecords = await db
        .select()
        .from(privilegeRecords)
        .where(and(
          eq(privilegeRecords.userId, userId),
          eq(privilegeRecords.status, 'active'),
          gte(privilegeRecords.endDate, now)
        ))


      // 按奖励类型分组，避免重复奖励
      const rewardTypeGroups = new Map<string, any[]>()

      for (const record of activeRecords) {
        const rewardType = record.rewardType
        if (!rewardTypeGroups.has(rewardType)) {
          rewardTypeGroups.set(rewardType, [])
        }
        rewardTypeGroups.get(rewardType)!.push(record)
      }

      // 为每种奖励类型检查是否可领取
      for (const [rewardType, records] of rewardTypeGroups) {
        // 检查该类型的所有记录是否今日已领取
        const todayAlreadyClaimed = records.some(record => {
          const claimDates = (record.claimDates as string[]) || []
          return claimDates.some(date => isToday(date))
        })

        if (!todayAlreadyClaimed) {
          rewards.push({
            id: `${rewardType}_${today}`,
            type: 'daily',
            rewardType: rewardType,
            rewards: REWARD_CONFIG[rewardType].daily
         })
        }
      }

      return rewards
    }
    catch (error) {
      console.error('获取可领取奖励失败', error)
      throw error
    }
  }

 
}
