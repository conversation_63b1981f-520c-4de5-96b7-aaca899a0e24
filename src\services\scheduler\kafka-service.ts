import type { Consumer, Producer } from 'kafkajs'
import { Kafka } from 'kafkajs'
import { consumerConfig, kafkaConfig, producerConfig, topics } from '@/config/kafka'
import { schedulerLogger } from '@/lib/logger'
// import { startMessageConsumer } from './douyin-message'

let producer: Producer | null = null
let consumer: Consumer | null = null

export async function initKafka() {
  const kafka = new Kafka(kafkaConfig)
  producer = kafka.producer(producerConfig)
  consumer = kafka.consumer(consumerConfig)

  try {
    await producer.connect()
    await consumer.connect()
    schedulerLogger.info('Kafka连接成功')

    // 订阅定时任务主题
    await consumer.subscribe({ topic: topics.SCHEDULED_TASKS })

    // 启动消息消费者
    // await startMessageConsumer(consumer)
  }
  catch (error) {
    schedulerLogger.error('Kafka连接失败', { message: error.message })
    throw error
  }
}
export async function shutdownKafka() {
  try {
    if (consumer) {
      await consumer.disconnect()
      schedulerLogger.info('Kafka consumer已断开连接')
    }

    if (producer) {
      await producer.disconnect()
      schedulerLogger.info('Kafka producer已断开连接')
    }
  }
  catch (error) {
    schedulerLogger.error('Kafka关闭时发生错误', { message: error.message })
    throw error
  }
  finally {
    producer = null
    consumer = null
  }
}

process.on('SIGTERM', async () => {
  console.log('收到 SIGTERM 信号')
  await shutdownKafka()
  process.exit(0)
})

process.on('SIGINT', async () => {
  console.log('收到 SIGINT 信号')
  await shutdownKafka()
  process.exit(0)
})
