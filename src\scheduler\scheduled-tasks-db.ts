import type { ScheduledMessageTask } from '@/config'
import { schedulerLogger } from '@/lib/logger'
import { getTime } from '@/utils/time'

/**
 * 数据库表分区配置
 */
interface PartitionConfig {
  tableName: string
  partitionColumn: string
  partitionType: 'monthly' | 'yearly'
  retentionMonths?: number
  description: string
}

/**
 * 自动分区配置表
 */
const PARTITION_CONFIGS: PartitionConfig[] = [
  {
    tableName: 'event_data',
    partitionColumn: 'event_time',
    partitionType: 'monthly',
    retentionMonths: 12, // 保留12个月数据
    description: '事件数据表 - 按月分区（每月600万数据）',
  },
  {
    tableName: 'game_archives',
    partitionColumn: 'user_created_at',
    partitionType: 'monthly',
    description: '游戏存档表 - 按用户创建时间月分区',
  },
  {
    tableName: 'privilege_records',
    partitionColumn: 'created_at',
    partitionType: 'monthly',
    description: '特权记录表 - 按创建时间月分区',
  },
  {
    tableName: 'mail_data',
    partitionColumn: 'created_at',
    partitionType: 'monthly',
    description: '邮件数据表 - 按创建时间月分区',
  },
]

/**
 * 本地定时任务配置
 */
const LOCAL_SCHEDULED_TASKS: ScheduledMessageTask[] = [
  // 自动分区管理任务
  {
    tplId: 'create_partitions',
    messageData: [],
    platform: 'system',
    appId: 'partition_manager',
    max: 1,
    cronExpression: '0 2 25 * *', // 每月25日凌晨2点执行
    intervalHours: 720, // 30天
    enabled: true,
    type: 'recurring',
    meta: {
      taskType: 'partition',
      description: '创建下个月的分区表',
      partitionConfigs: PARTITION_CONFIGS,
    },
  },
  {
    tplId: 'cleanup_partitions',
    messageData: [],
    platform: 'system',
    appId: 'partition_manager',
    max: 1,
    cronExpression: '0 3 1 * *', // 每月1日凌晨3点执行
    intervalHours: 720, // 30天
    enabled: true,
    type: 'recurring',
    meta: {
      taskType: 'partition',
      description: '清理过期的分区表',
      partitionConfigs: PARTITION_CONFIGS,
    },
  },
  {
    tplId: 'partition_stats',
    messageData: [],
    platform: 'system',
    appId: 'partition_manager',
    max: 1,
    cronExpression: '0 4 * * 0', // 每周日凌晨4点执行
    intervalHours: 168, // 7天
    enabled: true,
    type: 'recurring',
    meta: {
      taskType: 'partition',
      description: '生成分区统计报告',
      partitionConfigs: PARTITION_CONFIGS,
    },
  },
]

/**
 * 获取分区配置
 */
export function getPartitionConfigs(): PartitionConfig[] {
  return PARTITION_CONFIGS
}

/**
 * 根据表名获取分区配置
 */
export function getPartitionConfigByTable(tableName: string): PartitionConfig | undefined {
  return PARTITION_CONFIGS.find(config => config.tableName === tableName)
}

/**
 * 生成分区表名
 */
export function generatePartitionName(tableName: string, year: number, month?: number): string {
  if (month) {
    const monthStr = month.toString().padStart(2, '0')
    return `${tableName}_${year}_${monthStr}`
  }
  return `${tableName}_${year}`
}

/**
 * 获取需要创建的分区列表（未来3个月）
 */
export function getPartitionsToCreate(): Array<{ config: PartitionConfig, year: number, month: number }> {
  const partitions = []
  const now = getTime()

  // 创建未来3个月的分区
  for (let i = 0; i < 3; i++) {
    const targetDate = getTime(new Date(now.getFullYear(), now.getMonth() + i + 1, 1))
    const year = targetDate.getFullYear()
    const month = targetDate.getMonth() + 1

    for (const config of PARTITION_CONFIGS) {
      if (config.partitionType === 'monthly') {
        partitions.push({ config, year, month })
      }
    }
  }

  return partitions
}

/**
 * 获取需要清理的分区列表
 */
export function getPartitionsToCleanup(): Array<{ config: PartitionConfig, year: number, month: number }> {
  const partitions = []
  const now = getTime()

  for (const config of PARTITION_CONFIGS) {
    if (config.retentionMonths && config.partitionType === 'monthly') {
      // 计算需要清理的月份
      const cutoffDate = getTime(new Date(now.getFullYear(), now.getMonth() - config.retentionMonths, 1))

      // 清理更早的分区（假设清理前6个月的过期分区）
      for (let i = 0; i < 6; i++) {
        const targetDate = getTime(new Date(cutoffDate.getFullYear(), cutoffDate.getMonth() - i, 1))
        const year = targetDate.getFullYear()
        const month = targetDate.getMonth() + 1

        if (year > 2020) { // 避免清理过于久远的分区
          partitions.push({ config, year, month })
        }
      }
    }
  }

  return partitions
}

/**
 * 从本地配置加载定时任务配置
 */
export async function loadScheduledTasksFromDB(): Promise<ScheduledMessageTask[]> {
  try {
    schedulerLogger.info('从本地配置加载定时任务配置', {
      totalTasks: LOCAL_SCHEDULED_TASKS.length,
      enabledTasks: LOCAL_SCHEDULED_TASKS.filter(t => t.enabled).length,
      partitionTables: PARTITION_CONFIGS.length,
    })

    // 返回启用的任务
    return LOCAL_SCHEDULED_TASKS.filter(task => task.enabled)
  }
  catch (error) {
    schedulerLogger.error('从本地配置加载定时任务配置失败', {
      message: error.message || 'Unknown error',
      name: error.name || 'Error',
    })

    console.error('本地配置加载失败详细信息:', error)
    return []
  }
}

/**
 * 添加新的定时任务到本地配置
 * 支持分区管理任务的动态创建
 */
export async function createScheduledTask(
  taskData: Partial<ScheduledMessageTask>,
): Promise<number> {
  try {
    schedulerLogger.info('创建定时任务（本地配置模式）', {
      tplId: taskData.tplId,
      messageCount: Array.isArray(taskData.messageData) ? taskData.messageData.length : 0,
      taskType: taskData.meta?.taskType || 'message',
    })

    // 如果是分区管理任务，自动添加分区配置
    if (taskData.meta?.taskType === 'partition') {
      taskData.meta.partitionConfigs = PARTITION_CONFIGS
    }

    const mockId = Date.now()

    schedulerLogger.info('任务配置已准备', {
      tplId: taskData.tplId,
      hasPartitionConfig: !!taskData.meta?.partitionConfigs,
      partitionTablesCount: taskData.meta?.partitionConfigs?.length || 0,
    })

    return mockId
  }
  catch (error) {
    schedulerLogger.error('创建定时任务失败', {
      message: error.message,
      tplId: taskData.tplId,
    })
    throw error
  }
}

/**
 * 更新任务启用状态（本地配置模式）
 * 注意：这是一个占位函数，实际使用时需要修改 LOCAL_SCHEDULED_TASKS 数组
 */
export async function updateTaskEnabled(tplId: string, enabled: boolean): Promise<void> {
  try {
    schedulerLogger.info('更新任务状态（本地配置模式）', { tplId, enabled })
    schedulerLogger.warn('本地配置模式：请手动修改 LOCAL_SCHEDULED_TASKS 数组中对应任务的 enabled 属性')
  }
  catch (error) {
    schedulerLogger.error('更新任务状态失败', {
      message: error.message,
      tplId,
      enabled,
    })
    throw error
  }
}

/**
 * 删除定时任务（本地配置模式）
 * 注意：这是一个占位函数，实际使用时需要从 LOCAL_SCHEDULED_TASKS 数组中移除
 */
export async function deleteScheduledTask(tplId: string): Promise<void> {
  try {
    schedulerLogger.info('删除定时任务（本地配置模式）', { tplId })
    schedulerLogger.warn('本地配置模式：请手动从 LOCAL_SCHEDULED_TASKS 数组中移除对应任务')
  }
  catch (error) {
    schedulerLogger.error('删除定时任务失败', {
      message: error.message,
      tplId,
    })
    throw error
  }
}

/**
 * 获取所有任务列表（包括禁用的）
 */
export async function getAllScheduledTasks(): Promise<ScheduledMessageTask[]> {
  try {
    schedulerLogger.info('获取所有任务列表（本地配置模式）', {
      totalTasks: LOCAL_SCHEDULED_TASKS.length,
      partitionTasks: LOCAL_SCHEDULED_TASKS.filter(t => t.meta?.taskType === 'partition').length,
      messageTasks: LOCAL_SCHEDULED_TASKS.filter(t => t.meta?.taskType !== 'partition').length,
    })
    return LOCAL_SCHEDULED_TASKS
  }
  catch (error) {
    schedulerLogger.error('获取任务列表失败', { message: error.message })
    throw error
  }
}

/**
 * 获取分区管理任务列表
 */
export async function getPartitionTasks(): Promise<ScheduledMessageTask[]> {
  try {
    const partitionTasks = LOCAL_SCHEDULED_TASKS.filter(task => task.meta?.taskType === 'partition')
    schedulerLogger.info('获取分区管理任务列表', {
      totalPartitionTasks: partitionTasks.length,
      enabledPartitionTasks: partitionTasks.filter(t => t.enabled).length,
    })
    return partitionTasks
  }
  catch (error) {
    schedulerLogger.error('获取分区管理任务列表失败', { message: error.message })
    throw error
  }
}

/**
 * 导出分区配置类型供其他模块使用
 */
export type { PartitionConfig }
