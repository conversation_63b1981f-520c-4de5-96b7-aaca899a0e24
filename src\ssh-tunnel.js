import { Client } from 'ssh2';
import net from 'net';

// SSH连接配置
const sshConfig = {
  host: '************',
  port: 22,
  username: 'root',
  password: 'Y4%kF6dS^aP9w'
};

// 多个端口转发配置
const tunnelConfigs = [
  {
    name: 'PG',
    srcHost: '127.0.0.1',
    srcPort: 5432,
    dstHost: '127.0.0.1',
    dstPort: 5432 
  },
  {
    name: '<PERSON><PERSON>',
    srcHost: '127.0.0.1',
    srcPort: 6379,
    dstHost: '127.0.0.1',
    dstPort: 6379
  },
  {
    name: '<PERSON>f<PERSON>',
    srcHost: '127.0.0.1',
    srcPort: 9092,
    dstHost: '127.0.0.1',  // 改为127.0.0.1，因为是服务器本地的Docker容器
    dstPort: 9092
  },
  {
    name: 'Zookeeper',
    srcHost: '127.0.0.1',
    srcPort: 2181,
    dstHost: '127.0.0.1',
    dstPort: 2181
  }
];

console.log('🚀 正在建立SSH隧道连接...');
console.log(`📡 连接到: ${sshConfig.host}:${sshConfig.port}`);
console.log(`👤 用户: ${sshConfig.username}`);

const conn = new Client();
const servers = [];

conn.on('ready', () => {
  console.log('✅ SSH连接已建立');

  // 为每个端口配置创建隧道
  tunnelConfigs.forEach((config) => {
    // 创建本地服务器来转发连接
    const server = net.createServer((sock) => {
      console.log(`🔗 新的${config.name}连接请求`);

      conn.forwardOut(
        sock.remoteAddress,
        sock.remotePort,
        config.dstHost,
        config.dstPort,
        (err, stream) => {
          if (err) {
            console.error(`❌ ${config.name} SSH隧道创建失败:`, err);
            sock.end();
            return;
          }

          sock.pipe(stream);
          stream.pipe(sock);

          sock.on('close', () => {
            console.log(`🔌 ${config.name}连接已关闭`);
          });
        }
      );
    });

    server.listen(config.srcPort, config.srcHost, () => {
      console.log(`✅ ${config.name} SSH隧道已建立`);
      console.log(`📍 本地端口: ${config.srcHost}:${config.srcPort}`);
      console.log(`🎯 远程端口: ${config.dstHost}:${config.dstPort}`);
    });

    server.on('error', (err) => {
      console.error(`❌ ${config.name}本地服务器错误:`, err);
    });

    servers.push(server);
  });

  console.log('💡 现在可以通过以下端口访问远程服务:');
  console.log('   - MySQL: 127.0.0.1:5432 → 服务器:3307');
  console.log('   - Redis: 127.0.0.1:6379 → 服务器:6379');
  console.log('   - Kafka: 127.0.0.1:9092 → 服务器:9092');
  console.log('   - Zookeeper: 127.0.0.1:2181 → 服务器:2181');
  console.log('⏹️  按 Ctrl+C 关闭所有隧道');

  // 保持连接
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭所有SSH隧道...');
    servers.forEach(server => server.close());
    conn.end();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 正在关闭所有SSH隧道...');
    servers.forEach(server => server.close());
    conn.end();
    process.exit(0);
  });
});

conn.on('error', (err) => {
  console.error('❌ SSH连接错误:', err);
  console.error('请检查：');
  console.error('1. 网络连接是否正常');
  console.error('2. SSH服务器地址和端口是否正确');
  console.error('3. 用户名和密码是否正确');
});

conn.on('close', () => {
  console.log('🔌 SSH连接已关闭');
});

console.log('🔐 正在连接SSH服务器...');
conn.connect(sshConfig); 