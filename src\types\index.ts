import { describeRoute } from 'hono-openapi'
import { resolver } from 'hono-openapi/zod'
import z from 'zod'
// import 'zod-openapi'

export const rewardEnum = {
  blindBox: 'blindBox', // 盲盒
  adTicket: 'adTicket', // 广告券
  energy: 'energy', // 体力
} as const

export function responseSchema(responseSchema) {
  return z.object({
    success: z.boolean(),
    message: z.string(),
    data: responseSchema || z.object({}),
  })
}

export function globalResponse({ response, description, tags }: Record<string, any> = {}) {
  return describeRoute({
    tags: tags || ['默认'],
    description,
    responses: {
      200: {
        description: '请求成功',
        content: {
          'application/json': {
            schema: resolver(responseSchema(response)),
          },
        },
      },
      400: {
        description: '请求参数错误',
        content: {
          'application/json': {
            schema: resolver(z.object({
              success: z.boolean(),
              message: z.string(),
            })),
          },
        },
      },
      500: {
        description: '服务器内部错误',
        content: {
          'application/json': {
            schema: resolver(z.object({
              success: z.boolean(),
              message: z.string(),
            })),
          },
        },
      },
    },
  })
}

export const jsonSchema = z.record(z.string(), z.any()).meta({
  example: '{}',
  description: 'JSON格式数据(map)',
})
export const jsonStringSchema = z.string().meta({
  example: '{}',
  description: 'JSON格式数据(字符串)',
})
