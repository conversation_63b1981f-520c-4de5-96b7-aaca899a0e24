import { Hono } from 'hono'
import { describeRoute } from 'hono-openapi'
import { resolver, validator as z<PERSON><PERSON><PERSON>tor } from 'hono-openapi/zod'
import z from 'zod'
import { formatDate } from '@/common'
import { PRIVILEGE_CONFIG, REWARD_CONFIG } from '@/config'
import { jwtAuth } from '@/lib/jwt'
import { PrivilegeCardService, rewardType } from '@/services/privilege/privilege-card-service'
import { globalResponse, rewardEnum } from '@/types'
import { purchaseSchema, rewardClaimSchema, userIdSchema } from '@/types/privilege'

const router = new Hono()
router.use('/*', jwtAuth())

const privilegeCardService = new PrivilegeCardService()

// 获取用户特权状态
router.post('/status', globalResponse({
  description: '获取用户特权状态',
  tags: ['特权卡'],
}), zValidator('json', userIdSchema), async (c) => {
  try {
    const { userId } = await c.req.json()

    if (!userId) {
      return c.json({ success: false, message: '缺少用户ID' }, 400)
    }

    const status = await privilegeCardService.getUserPrivilegeStatus(userId)
    return c.json({
      success: true,
      data: {
        ...status,
        lastUpdated: formatDate(status.lastUpdated),
      },
    })
  }
  catch (error) {
    console.error('获取特权状态失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 记录广告观看
router.post('/ad-watch', globalResponse({
  description: '记录广告观看',
  tags: ['特权卡'],
}), zValidator('json', userIdSchema), async (c) => {
  try {
    const { userId } = await c.req.json()

    if (!userId) {
      return c.json({ success: false, message: '缺少用户ID' }, 400)
    }

    const result = await privilegeCardService.recordAdWatch(userId)

    return c.json({
      success: true,
      data: result,
    })
  }
  catch (error) {
    console.error('记录广告观看失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 开始试用
router.post('/trial/start', globalResponse({
  description: '开始试用',
  tags: ['特权卡'],
}), zValidator('json', userIdSchema), async (c) => {
  try {
    const { userId } = await c.req.json()

    if (!userId) {
      return c.json({ success: false, message: '缺少用户ID' }, 400)
    }

    const trial = await privilegeCardService.startTrial(userId)

    return c.json({
      success: true,
      data: {
        ...trial,
        startTime: formatDate(trial.startTime),
        endTime: formatDate(trial.endTime),
        createdAt: formatDate(trial.createdAt),
      },
    })
  }
  catch (error) {
    console.error('开始试用失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 触发折扣
// router.post('/discount/trigger', async (c) => {
//   try {
//     const { userId } = await c.req.json()

//     if (!userId) {
//       return c.json({ success: false, message: '缺少用户ID' }, 400)
//     }

//     const discount = await privilegeCardService.triggerDiscount(userId)

//     return c.json({
//       success: true,
//       data: {
//         ...discount,
//         startTime: formatDate(discount.startTime),
//         endTime: formatDate(discount.endTime),
//         createdAt: formatDate(discount.createdAt),
//       },
//     })
//   }
//   catch (error) {
//     console.error('触发折扣失败', { error: error.message })
//     return c.json({ success: false, message: error.message }, 500)
//   }
// })

// 购买特权卡
router.post('/purchase', globalResponse({
  description: '购买特权卡',
  tags: ['特权卡'],
}), zValidator('json', purchaseSchema), async (c) => {
  try {
    const { userId, rewardType, gameId } = await c.req.json()
    const purchasePrice = 0
    const originalPrice = 0
    const transactionId = ''
    const purchaseSource = ''
    if (!userId || !rewardType || !gameId) {
      return c.json({ success: false, message: '缺少必要参数' }, 400)
    }

    const card = await privilegeCardService.purchaseCard({
      userId,
      gameId,
      rewardType,
      purchasePrice,
      originalPrice,
      transactionId,
      purchaseSource,
    })

    return c.json({
      success: true,
      data: {
        ...card,
        startDate: formatDate(card.startDate),
        endDate: formatDate(card.endDate),
        createdAt: formatDate(card.createdAt),
        updatedAt: formatDate(card.updatedAt),
      },
    })
  }
  catch (error) {
    console.error('购买特权卡失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

// 领取奖励
router.post('/reward/claim', globalResponse({
  description: '领取奖励',
  tags: ['特权卡'],
}), zValidator('json', rewardClaimSchema), async (c) => {
  try {
    const { userId, rewardType } = await c.req.json()

    if (!userId || !rewardType) {
      return c.json({ success: false, message: '缺少必要参数' }, 400)
    }

    const reward = await privilegeCardService.claimReward(userId, rewardType)

    return c.json({
      success: true,
      data: {
        ...reward,
        rewardDate: formatDate(reward.rewardDate),
        claimedAt: formatDate(reward.claimedAt),
        createdAt: formatDate(reward.createdAt),
      },
    })
  }
  catch (error) {
    console.error('领取奖励失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})
// 获取配置
router.post('/getConfig', globalResponse({
  description: '获取配置',
  tags: ['特权卡'],
}), async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        ...PRIVILEGE_CONFIG,

      },
    })
  }
  catch (error) {
    console.error('获取每日奖励失败', { error: error.message })
    return c.json({ success: false, message: error.message }, 500)
  }
})

export default router
// 获取可领取奖励列表
// router.post('/rewards', async (c) => {
//   try {
//     const { userId } = await c.req.json()

//     if (!userId) {
//       return c.json({ success: false, message: '缺少用户ID' }, 400)
//     }

//     const rewards = await privilegeCardService.getClaimableRewards(userId)

//     return c.json({
//       success: true,
//       data: rewards.map(reward => ({
//         ...reward,
//         rewardDate: formatDate(reward.rewardDate),
//         claimedAt: formatDate(reward.claimedAt),
//         createdAt: formatDate(reward.createdAt),
//       })),
//     })
//   }
//   catch (error) {
//     console.error('获取奖励列表失败', { error: error.message })
//     return c.json({ success: false, message: error.message }, 500)
//   }
// })

// 获取用户当天可领取的奖励详情
// router.post('/daily-rewards', async (c) => {
//   try {
//     const { userId } = await c.req.json()

//     if (!userId) {
//       return c.json({ success: false, message: '缺少用户ID' }, 400)
//     }

//     const rewards = await privilegeCardService.getClaimableRewards(userId)

//     // 分类整理奖励
//     const dailyRewards = {
//       trial: null, // 试用月卡奖励
//       monthly: null, // 月卡每日奖励
//       weekly: null, // 周卡每日奖励
//     }

//     for (const reward of rewards) {
//       dailyRewards[reward.rewardType] = {
//         id: reward.id,
//         type: rewardType[reward.rewardType],
//         rewards: reward.rewards,
//         available: reward.available,
//       }
//     }

//     return c.json({
//       success: true,
//       data: {
//         userId,
//         date: getTime().toISOString().split('T')[0],
//         rewards: dailyRewards,
//         summary: {
//           hasTrialReward: !!dailyRewards.trial,
//           hasMonthlyReward: !!dailyRewards.monthly,
//           hasWeeklyReward: !!dailyRewards.weekly,
//         },
//       },
//     })
//   }
//   catch (error) {
//     console.error('获取每日奖励失败', { error: error.message })
//     return c.json({ success: false, message: error.message }, 500)
//   }
// })
